"""
Fast training script for all prediction horizons (1d, 7d, 30d)
Optimized for speed while maintaining quality
"""

import pandas as pd
import numpy as np
from pathlib import Path
import joblib
import json
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load preprocessed data"""
    print("Loading preprocessed data...")
    data_path = Path('data/processed/bitcoin_preprocessed.csv')
    if not data_path.exists():
        raise FileNotFoundError("Preprocessed data not found. Run feature engineering first.")
    
    data = pd.read_csv(data_path, index_col=0, parse_dates=True)
    print(f"Data loaded: {data.shape}")
    
    # Simple train/test split (80/20)
    split_idx = int(len(data) * 0.8)
    train_data = data.iloc[:split_idx]
    test_data = data.iloc[split_idx:]
    
    print(f"Train: {train_data.shape}, Test: {test_data.shape}")
    return train_data, test_data

def prepare_features_and_targets(train_data, test_data):
    """Prepare features and target variables"""
    print("Preparing features and targets...")
    
    # Define target columns
    target_columns = ['target_return_1d', 'target_return_7d', 'target_return_30d']
    
    # Feature columns (exclude targets and non-predictive columns)
    exclude_cols = target_columns + ['close', 'open', 'high', 'low', 'adj_close', 'volume']
    feature_columns = [col for col in train_data.columns if col not in exclude_cols]
    
    # Prepare features
    X_train = train_data[feature_columns].fillna(0)
    X_test = test_data[feature_columns].fillna(0)
    
    # Prepare targets
    targets = {}
    for target in target_columns:
        if target in train_data.columns:
            y_train = train_data[target].fillna(0)
            y_test = test_data[target].fillna(0)
            targets[target] = (y_train, y_test)
    
    print(f"Features: {len(feature_columns)} columns")
    print(f"Targets: {len(targets)} columns")
    
    return X_train, X_test, targets, feature_columns

def train_model(model_type, X_train, y_train, X_test, y_test, target_name):
    """Train a single model with optimized parameters"""
    print(f"\nTraining {model_type.upper()} for {target_name}...")
    
    if model_type == 'randomforest':
        # Optimized RF parameters for speed
        model = RandomForestRegressor(
            n_estimators=100,  # Reduced for speed
            max_depth=15,
            min_samples_split=10,
            min_samples_leaf=5,
            random_state=42,
            n_jobs=-1
        )
    elif model_type == 'xgboost':
        # Optimized XGB parameters
        model = xgb.XGBRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            verbosity=0
        )
    elif model_type == 'lightgbm':
        # Optimized LGB parameters
        model = lgb.LGBMRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            n_jobs=-1,
            verbosity=-1
        )
    
    # Train model
    model.fit(X_train, y_train)
    
    # Make predictions
    y_pred = model.predict(X_test)
    
    # Calculate metrics
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)
    
    # Calculate directional accuracy
    actual_direction = np.sign(y_test)
    predicted_direction = np.sign(y_pred)
    directional_accuracy = np.mean(actual_direction == predicted_direction)
    
    metrics = {
        'mse': float(mse),
        'rmse': float(rmse),
        'mae': float(mae),
        'r2_score': float(r2),
        'directional_accuracy': float(directional_accuracy)
    }
    
    print(f"  R² Score: {r2:.4f}")
    print(f"  RMSE: {rmse:.4f}")
    print(f"  Directional Accuracy: {directional_accuracy:.4f}")
    
    return model, metrics

def save_model_and_results(model, metrics, model_type, target_name):
    """Save model and results"""
    # Create directories
    Path('models').mkdir(exist_ok=True)
    Path('results').mkdir(exist_ok=True)
    
    # Save model
    model_filename = f'models/{model_type}_{target_name}_model.joblib'
    joblib.dump(model, model_filename)
    print(f"  Saved: {model_filename}")
    
    return metrics

def main():
    """Main training function"""
    print("Bitcoin Price Prediction - Fast Multi-Horizon Training")
    print("=" * 60)
    
    try:
        # Load data
        train_data, test_data = load_data()
        
        # Prepare features and targets
        X_train, X_test, targets, feature_columns = prepare_features_and_targets(train_data, test_data)
        
        # Scale features
        print("\nScaling features...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Save scaler
        joblib.dump(scaler, 'models/scaler_standard.joblib')
        print("Scaler saved")
        
        # Model types to train
        model_types = ['randomforest', 'xgboost', 'lightgbm']
        
        # Store all results
        all_results = {}
        
        # Train models for each target and model type
        for target_name, (y_train, y_test) in targets.items():
            print(f"\n{'='*60}")
            print(f"TRAINING MODELS FOR {target_name.upper()}")
            print(f"{'='*60}")
            
            target_results = {}
            
            for model_type in model_types:
                try:
                    model, metrics = train_model(
                        model_type, X_train_scaled, y_train, X_test_scaled, y_test, target_name
                    )
                    
                    # Save model
                    save_model_and_results(model, metrics, model_type, target_name)
                    
                    # Store results
                    target_results[model_type] = metrics
                    
                except Exception as e:
                    print(f"  Error training {model_type}: {e}")
                    continue
            
            all_results[target_name] = target_results
        
        # Save comprehensive results
        results_file = 'results/tree_models_results.json'
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        
        print(f"\n{'='*60}")
        print("TRAINING SUMMARY")
        print(f"{'='*60}")
        
        # Display summary
        for target_name, target_results in all_results.items():
            print(f"\n{target_name.upper()}:")
            for model_type, metrics in target_results.items():
                print(f"  {model_type:12}: R² = {metrics['r2_score']:.4f}, "
                      f"RMSE = {metrics['rmse']:.4f}, "
                      f"Dir.Acc = {metrics['directional_accuracy']:.4f}")
        
        print(f"\nResults saved to: {results_file}")
        print("All models trained successfully!")
        
        return all_results
        
    except Exception as e:
        print(f"Training failed: {e}")
        raise

if __name__ == "__main__":
    results = main()
