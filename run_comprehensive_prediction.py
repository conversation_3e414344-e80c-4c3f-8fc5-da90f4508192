"""
Comprehensive Bitcoin Price Prediction System
Supports 1d, 7d, and 30d predictions with proper feature alignment
"""

import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load the preprocessed data"""
    print("📊 Loading preprocessed data...")
    
    data_path = Path('data/processed/bitcoin_preprocessed.csv')
    if not data_path.exists():
        raise FileNotFoundError("Preprocessed data not found. Run feature engineering first.")
    
    data = pd.read_csv(data_path, index_col=0, parse_dates=True)
    print(f"✅ Data loaded: {data.shape}")
    print(f"📅 Date range: {data.index.min().date()} to {data.index.max().date()}")
    
    return data

def load_models_and_scaler():
    """Load all trained models and scaler"""
    print("\n🤖 Loading trained models...")
    
    models = {}
    model_files = {
        'randomforest_1d': 'models/randomforest_target_return_1d_model.joblib',
        'randomforest_7d': 'models/randomforest_target_return_7d_model.joblib',
        'randomforest_30d': 'models/randomforest_target_return_30d_model.joblib',
        'xgboost_1d': 'models/xgboost_target_return_1d_model.joblib',
        'xgboost_7d': 'models/xgboost_target_return_7d_model.joblib',
        'xgboost_30d': 'models/xgboost_target_return_30d_model.joblib',
        'lightgbm_1d': 'models/lightgbm_target_return_1d_model.joblib',
        'lightgbm_7d': 'models/lightgbm_target_return_7d_model.joblib',
        'lightgbm_30d': 'models/lightgbm_target_return_30d_model.joblib'
    }
    
    for model_name, file_path in model_files.items():
        try:
            models[model_name] = joblib.load(file_path)
            print(f"✅ Loaded {model_name}")
        except Exception as e:
            print(f"⚠️ Could not load {model_name}: {e}")
    
    # Load scaler
    try:
        scaler = joblib.load('models/scaler_standard.joblib')
        print("✅ Loaded scaler")
    except Exception as e:
        print(f"⚠️ Could not load scaler: {e}")
        scaler = None
    
    return models, scaler

def prepare_features_for_prediction(data):
    """Prepare features for prediction, ensuring proper alignment"""
    print("\n🔧 Preparing features for prediction...")
    
    # Get the most recent data point
    recent_data = data.tail(1).copy()
    
    # Define feature columns (exclude targets and OHLCV)
    exclude_cols = [
        'target_return_1d', 'target_return_7d', 'target_return_30d',
        'open', 'high', 'low', 'close', 'adj_close', 'volume'
    ]
    
    feature_cols = [col for col in recent_data.columns if col not in exclude_cols]
    X_recent = recent_data[feature_cols]
    
    print(f"📊 Using {len(feature_cols)} features for prediction")
    print(f"🕐 Most recent data point: {recent_data.index[0].date()}")
    print(f"💰 Current Bitcoin price: ${recent_data['close'].iloc[0]:,.2f}")
    
    return X_recent, recent_data['close'].iloc[0]

def make_predictions(models, scaler, X_recent):
    """Make predictions with all models"""
    print("\n🔮 Making predictions...")
    
    # Scale features if scaler is available
    if scaler is not None:
        try:
            X_scaled = scaler.transform(X_recent)
            print("✅ Features scaled successfully")
        except Exception as e:
            print(f"⚠️ Scaling failed: {e}")
            X_scaled = X_recent.values
    else:
        X_scaled = X_recent.values
    
    predictions = {}
    
    # Make predictions with each model
    for model_name, model in models.items():
        try:
            pred = model.predict(X_scaled)[0]
            predictions[model_name] = pred
            
            # Extract model type and horizon
            parts = model_name.split('_')
            model_type = parts[0]
            horizon = parts[1]
            
            print(f"📊 {model_type.upper()} ({horizon}): {pred:.4f} ({pred*100:+.2f}%)")
            
        except Exception as e:
            print(f"❌ Error with {model_name}: {e}")
    
    return predictions

def calculate_ensemble_predictions(predictions):
    """Calculate ensemble predictions for each time horizon"""
    print("\n🎯 Ensemble Predictions:")
    
    horizons = ['1d', '7d', '30d']
    ensemble_preds = {}
    
    for horizon in horizons:
        # Get all predictions for this horizon
        horizon_preds = [pred for name, pred in predictions.items() if name.endswith(horizon)]
        
        if horizon_preds:
            ensemble_pred = np.mean(horizon_preds)
            ensemble_preds[horizon] = ensemble_pred
            
            # Determine trend direction
            trend = "📈 BULLISH" if ensemble_pred > 0 else "📉 BEARISH"
            
            print(f"🔮 {horizon} return prediction: {ensemble_pred:.4f} ({ensemble_pred*100:+.2f}%) {trend}")
        else:
            print(f"⚠️ No predictions available for {horizon}")
    
    return ensemble_preds

def calculate_price_targets(current_price, ensemble_preds):
    """Calculate price targets based on return predictions"""
    print(f"\n💰 Price Targets (Current: ${current_price:,.2f}):")
    
    price_targets = {}
    
    for horizon, return_pred in ensemble_preds.items():
        target_price = current_price * (1 + return_pred)
        price_change = target_price - current_price
        price_targets[horizon] = target_price
        
        direction = "⬆️" if price_change > 0 else "⬇️"
        print(f"🎯 {horizon} target: ${target_price:,.2f} ({direction} ${abs(price_change):,.2f})")
    
    return price_targets

def display_model_performance():
    """Display model performance metrics"""
    print("\n📊 Model Performance Summary:")
    
    try:
        with open('results/tree_models_results.json', 'r') as f:
            import json
            results = json.load(f)
        
        for target, models in results.items():
            print(f"\n{target.upper()}:")
            for model_name, metrics in models.items():
                r2 = metrics.get('r2_score', 0)
                rmse = metrics.get('rmse', 0)
                dir_acc = metrics.get('directional_accuracy', 0)
                print(f"  {model_name:12}: R² = {r2:+.3f}, RMSE = {rmse:.4f}, Dir.Acc = {dir_acc:.3f}")
    
    except Exception as e:
        print(f"⚠️ Could not load performance metrics: {e}")

def create_prediction_summary(current_price, ensemble_preds, price_targets):
    """Create a comprehensive prediction summary"""
    print("\n" + "="*60)
    print("📋 BITCOIN PRICE PREDICTION SUMMARY")
    print("="*60)
    
    print(f"📅 Prediction Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"💰 Current Price: ${current_price:,.2f}")
    
    print(f"\n🔮 Predicted Returns:")
    for horizon, return_pred in ensemble_preds.items():
        trend = "BULLISH 📈" if return_pred > 0 else "BEARISH 📉"
        print(f"  {horizon:3}: {return_pred*100:+6.2f}% ({trend})")
    
    print(f"\n🎯 Price Targets:")
    for horizon, target_price in price_targets.items():
        change = target_price - current_price
        change_pct = (change / current_price) * 100
        print(f"  {horizon:3}: ${target_price:8,.2f} ({change_pct:+6.2f}%)")
    
    # Risk assessment
    print(f"\n⚠️ Risk Assessment:")
    avg_return = np.mean(list(ensemble_preds.values()))
    if avg_return > 0.05:
        risk = "HIGH BULLISH"
    elif avg_return > 0.02:
        risk = "MODERATE BULLISH"
    elif avg_return > -0.02:
        risk = "NEUTRAL"
    elif avg_return > -0.05:
        risk = "MODERATE BEARISH"
    else:
        risk = "HIGH BEARISH"
    
    print(f"  Overall Sentiment: {risk}")
    print(f"  Average Return: {avg_return*100:+.2f}%")

def main():
    """Main prediction function"""
    print("🚀 Bitcoin Price Prediction System - Multi-Horizon Analysis")
    print("="*70)
    
    try:
        # Load data
        data = load_data()
        
        # Load models
        models, scaler = load_models_and_scaler()
        
        if not models:
            print("❌ No models loaded. Please train models first.")
            return
        
        # Prepare features
        X_recent, current_price = prepare_features_for_prediction(data)
        
        # Make predictions
        predictions = make_predictions(models, scaler, X_recent)
        
        if not predictions:
            print("❌ No predictions generated.")
            return
        
        # Calculate ensemble predictions
        ensemble_preds = calculate_ensemble_predictions(predictions)
        
        # Calculate price targets
        price_targets = calculate_price_targets(current_price, ensemble_preds)
        
        # Display model performance
        display_model_performance()
        
        # Create summary
        create_prediction_summary(current_price, ensemble_preds, price_targets)
        
        print(f"\n✅ Prediction analysis complete!")
        
        return {
            'current_price': current_price,
            'predictions': predictions,
            'ensemble_predictions': ensemble_preds,
            'price_targets': price_targets
        }
        
    except Exception as e:
        print(f"❌ Prediction failed: {e}")
        raise

if __name__ == "__main__":
    results = main()
