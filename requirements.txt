# Core Data Science Libraries
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# Data Collection
yfinance>=0.2.18
requests>=2.31.0
python-dotenv>=1.0.0

# Machine Learning
scikit-learn>=1.3.0
xgboost>=1.7.0
lightgbm>=4.0.0

# Deep Learning (install separately if needed)
# tensorflow>=2.13.0
# torch>=2.0.0
# torchvision>=0.15.0

# Time Series Analysis
statsmodels>=0.14.0
pmdarima>=2.0.0  # Auto-ARIMA
arch>=6.2.0     # GARCH models

# Technical Analysis
# TA-Lib>=0.4.25  # Install manually if needed
ta>=0.10.2

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Data Processing
tqdm>=4.65.0
joblib>=1.3.0

# Configuration
PyYAML>=6.0

# Jupyter Notebooks
jupyter>=1.0.0
ipykernel>=6.25.0

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Code Quality
black>=23.7.0
flake8>=6.0.0
isort>=5.12.0
