#!/usr/bin/env python3
"""
Tree-based Machine Learning Models for Bitcoin Price Prediction
==============================================================

This script implements tree-based models including Random Forest, XGBoost, and LightGBM
for Bitcoin price prediction with comprehensive hyperparameter tuning and evaluation.

Author: Bitcoin Price Prediction Project
Date: 2025-07-06
"""

import pandas as pd
import numpy as np
import joblib
import json
import warnings
from pathlib import Path
from datetime import datetime
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import seaborn as sns

# Machine Learning Libraries
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import GridSearchCV, TimeSeriesSplit
import xgboost as xgb
import lightgbm as lgb

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load preprocessed data and splits"""
    print("Loading preprocessed data...")
    
    # Load main dataset
    data_path = Path("data/processed/bitcoin_preprocessed.csv")
    data = pd.read_csv(data_path, index_col=0, parse_dates=True)
    
    # Load train-test splits
    splits_dir = Path("data/splits/simple_split/fold_1")

    # Load simple split
    X_train = pd.read_csv(splits_dir / "X_train.csv", index_col=0, parse_dates=True)
    X_test = pd.read_csv(splits_dir / "X_test.csv", index_col=0, parse_dates=True)
    y_train = pd.read_csv(splits_dir / "y_train.csv", index_col=0, parse_dates=True)
    y_test = pd.read_csv(splits_dir / "y_test.csv", index_col=0, parse_dates=True)

    # Combine X and y for easier handling
    train_data = pd.concat([X_train, y_train], axis=1)
    test_data = pd.concat([X_test, y_test], axis=1)

    simple_split = {
        'train': train_data,
        'test': test_data
    }
    
    print(f"✓ Data loaded: {data.shape}")
    print(f"✓ Simple split - Train: {simple_split['train'].shape}, Test: {simple_split['test'].shape}")
    
    return data, simple_split

def prepare_features_targets(data):
    """Prepare features and target variables"""
    print("\nPreparing features and targets...")
    
    # Define target columns (using return targets for prediction)
    target_cols = ['target_return_1d', 'target_return_7d', 'target_return_30d']
    
    # Feature columns (exclude all target columns and price columns that might cause leakage)
    exclude_cols = [col for col in data.columns if col.startswith('target_')] + ['close', 'open', 'high', 'low', 'volume']
    feature_cols = [col for col in data.columns if col not in exclude_cols]
    
    print(f"✓ Features: {len(feature_cols)} columns")
    print(f"✓ Targets: {len(target_cols)} columns")
    
    return feature_cols, target_cols

def evaluate_model(y_true, y_pred, model_name, target_name):
    """Calculate evaluation metrics"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    # MAPE calculation (handle division by zero)
    mape = np.mean(np.abs((y_true - y_pred) / np.where(y_true != 0, y_true, 1))) * 100
    
    # Directional accuracy
    direction_true = np.sign(y_true)
    direction_pred = np.sign(y_pred)
    directional_accuracy = np.mean(direction_true == direction_pred) * 100
    
    metrics = {
        'model': model_name,
        'target': target_name,
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'mape': mape,
        'directional_accuracy': directional_accuracy
    }
    
    return metrics

def train_random_forest(X_train, y_train, X_test, y_test, target_name):
    """Train Random Forest with hyperparameter tuning"""
    print(f"\n{'='*50}")
    print(f"TRAINING RANDOM FOREST - {target_name.upper()}")
    print(f"{'='*50}")
    
    # Define parameter grid
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [10, 20, None],
        'min_samples_split': [2, 5, 10],
        'min_samples_leaf': [1, 2, 4],
        'max_features': ['sqrt', 'log2', None]
    }
    
    # Use TimeSeriesSplit for cross-validation
    tscv = TimeSeriesSplit(n_splits=5)
    
    # Grid search
    print("Performing hyperparameter tuning...")
    rf = RandomForestRegressor(random_state=42, n_jobs=-1)
    grid_search = GridSearchCV(
        rf, param_grid, cv=tscv, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )
    
    grid_search.fit(X_train, y_train)
    
    # Best model
    best_rf = grid_search.best_estimator_
    print(f"✓ Best parameters: {grid_search.best_params_}")
    
    # Predictions
    y_pred_train = best_rf.predict(X_train)
    y_pred_test = best_rf.predict(X_test)
    
    # Evaluation
    train_metrics = evaluate_model(y_train, y_pred_train, 'RandomForest', f'{target_name}_train')
    test_metrics = evaluate_model(y_test, y_pred_test, 'RandomForest', f'{target_name}_test')
    
    print(f"Train RMSE: {train_metrics['rmse']:.6f}")
    print(f"Test RMSE: {test_metrics['rmse']:.6f}")
    print(f"Test R²: {test_metrics['r2']:.4f}")
    print(f"Test MAPE: {test_metrics['mape']:.2f}%")
    print(f"Test Directional Accuracy: {test_metrics['directional_accuracy']:.2f}%")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': best_rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    return {
        'model': best_rf,
        'best_params': grid_search.best_params_,
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': feature_importance,
        'predictions': {
            'train': y_pred_train,
            'test': y_pred_test
        }
    }

def train_xgboost(X_train, y_train, X_test, y_test, target_name):
    """Train XGBoost with hyperparameter tuning"""
    print(f"\n{'='*50}")
    print(f"TRAINING XGBOOST - {target_name.upper()}")
    print(f"{'='*50}")
    
    # Define parameter grid
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [3, 6, 10],
        'learning_rate': [0.01, 0.1, 0.2],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0]
    }
    
    # Use TimeSeriesSplit for cross-validation
    tscv = TimeSeriesSplit(n_splits=5)
    
    # Grid search
    print("Performing hyperparameter tuning...")
    xgb_model = xgb.XGBRegressor(random_state=42, n_jobs=-1)
    grid_search = GridSearchCV(
        xgb_model, param_grid, cv=tscv, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )
    
    grid_search.fit(X_train, y_train)
    
    # Best model
    best_xgb = grid_search.best_estimator_
    print(f"✓ Best parameters: {grid_search.best_params_}")
    
    # Predictions
    y_pred_train = best_xgb.predict(X_train)
    y_pred_test = best_xgb.predict(X_test)
    
    # Evaluation
    train_metrics = evaluate_model(y_train, y_pred_train, 'XGBoost', f'{target_name}_train')
    test_metrics = evaluate_model(y_test, y_pred_test, 'XGBoost', f'{target_name}_test')
    
    print(f"Train RMSE: {train_metrics['rmse']:.6f}")
    print(f"Test RMSE: {test_metrics['rmse']:.6f}")
    print(f"Test R²: {test_metrics['r2']:.4f}")
    print(f"Test MAPE: {test_metrics['mape']:.2f}%")
    print(f"Test Directional Accuracy: {test_metrics['directional_accuracy']:.2f}%")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': best_xgb.feature_importances_
    }).sort_values('importance', ascending=False)
    
    return {
        'model': best_xgb,
        'best_params': grid_search.best_params_,
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': feature_importance,
        'predictions': {
            'train': y_pred_train,
            'test': y_pred_test
        }
    }

def train_lightgbm(X_train, y_train, X_test, y_test, target_name):
    """Train LightGBM with hyperparameter tuning"""
    print(f"\n{'='*50}")
    print(f"TRAINING LIGHTGBM - {target_name.upper()}")
    print(f"{'='*50}")
    
    # Define parameter grid
    param_grid = {
        'n_estimators': [100, 200, 300],
        'max_depth': [3, 6, 10],
        'learning_rate': [0.01, 0.1, 0.2],
        'subsample': [0.8, 0.9, 1.0],
        'colsample_bytree': [0.8, 0.9, 1.0],
        'num_leaves': [31, 50, 100]
    }
    
    # Use TimeSeriesSplit for cross-validation
    tscv = TimeSeriesSplit(n_splits=5)
    
    # Grid search
    print("Performing hyperparameter tuning...")
    lgb_model = lgb.LGBMRegressor(random_state=42, n_jobs=-1, verbose=-1)
    grid_search = GridSearchCV(
        lgb_model, param_grid, cv=tscv, scoring='neg_mean_squared_error',
        n_jobs=-1, verbose=1
    )
    
    grid_search.fit(X_train, y_train)
    
    # Best model
    best_lgb = grid_search.best_estimator_
    print(f"✓ Best parameters: {grid_search.best_params_}")
    
    # Predictions
    y_pred_train = best_lgb.predict(X_train)
    y_pred_test = best_lgb.predict(X_test)
    
    # Evaluation
    train_metrics = evaluate_model(y_train, y_pred_train, 'LightGBM', f'{target_name}_train')
    test_metrics = evaluate_model(y_test, y_pred_test, 'LightGBM', f'{target_name}_test')
    
    print(f"Train RMSE: {train_metrics['rmse']:.6f}")
    print(f"Test RMSE: {test_metrics['rmse']:.6f}")
    print(f"Test R²: {test_metrics['r2']:.4f}")
    print(f"Test MAPE: {test_metrics['mape']:.2f}%")
    print(f"Test Directional Accuracy: {test_metrics['directional_accuracy']:.2f}%")
    
    # Feature importance
    feature_importance = pd.DataFrame({
        'feature': X_train.columns,
        'importance': best_lgb.feature_importances_
    }).sort_values('importance', ascending=False)
    
    return {
        'model': best_lgb,
        'best_params': grid_search.best_params_,
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'feature_importance': feature_importance,
        'predictions': {
            'train': y_pred_train,
            'test': y_pred_test
        }
    }

def save_models_and_results(all_results):
    """Save trained models and results"""
    print("\nSaving models and results...")

    # Create directories
    models_dir = Path("models")
    results_dir = Path("results")
    models_dir.mkdir(exist_ok=True)
    results_dir.mkdir(exist_ok=True)

    # Save models
    for target_name, target_results in all_results.items():
        for model_name, model_data in target_results.items():
            model_filename = f"{model_name.lower()}_{target_name}_model.joblib"
            joblib.dump(model_data['model'], models_dir / model_filename)
            print(f"✓ Saved {model_filename}")

    # Prepare results for JSON export (remove non-serializable objects)
    export_results = {}
    for target_name, target_results in all_results.items():
        export_results[target_name] = {}
        for model_name, model_data in target_results.items():
            export_results[target_name][model_name] = {
                'best_params': model_data['best_params'],
                'train_metrics': model_data['train_metrics'],
                'test_metrics': model_data['test_metrics'],
                'feature_importance_top10': model_data['feature_importance'].head(10).to_dict('records')
            }

    # Save results to JSON
    results_file = results_dir / "tree_models_results.json"
    with open(results_file, 'w') as f:
        json.dump(export_results, f, indent=2, default=str)
    print(f"✓ Results saved to {results_file}")

    return export_results

def create_visualizations(all_results):
    """Create performance visualizations"""
    print("\nCreating visualizations...")

    results_dir = Path("results")

    # Collect metrics for comparison
    metrics_data = []
    for target_name, target_results in all_results.items():
        for model_name, model_data in target_results.items():
            test_metrics = model_data['test_metrics']
            metrics_data.append({
                'Target': target_name,
                'Model': model_name,
                'RMSE': test_metrics['rmse'],
                'R²': test_metrics['r2'],
                'MAPE': test_metrics['mape'],
                'Directional_Accuracy': test_metrics['directional_accuracy']
            })

    metrics_df = pd.DataFrame(metrics_data)

    # Create comparison plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Tree-based Models Performance Comparison', fontsize=16, fontweight='bold')

    # RMSE comparison
    sns.barplot(data=metrics_df, x='Target', y='RMSE', hue='Model', ax=axes[0,0])
    axes[0,0].set_title('RMSE Comparison')
    axes[0,0].tick_params(axis='x', rotation=45)

    # R² comparison
    sns.barplot(data=metrics_df, x='Target', y='R²', hue='Model', ax=axes[0,1])
    axes[0,1].set_title('R² Score Comparison')
    axes[0,1].tick_params(axis='x', rotation=45)

    # MAPE comparison
    sns.barplot(data=metrics_df, x='Target', y='MAPE', hue='Model', ax=axes[1,0])
    axes[1,0].set_title('MAPE Comparison (%)')
    axes[1,0].tick_params(axis='x', rotation=45)

    # Directional Accuracy comparison
    sns.barplot(data=metrics_df, x='Target', y='Directional_Accuracy', hue='Model', ax=axes[1,1])
    axes[1,1].set_title('Directional Accuracy (%)')
    axes[1,1].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    comparison_file = results_dir / "tree_models_performance_comparison.png"
    plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"✓ Performance comparison saved to {comparison_file}")

    # Feature importance plots for each target
    for target_name, target_results in all_results.items():
        fig, axes = plt.subplots(1, 3, figsize=(20, 6))
        fig.suptitle(f'Feature Importance - {target_name.upper()}', fontsize=16, fontweight='bold')

        for idx, (model_name, model_data) in enumerate(target_results.items()):
            top_features = model_data['feature_importance'].head(15)

            axes[idx].barh(range(len(top_features)), top_features['importance'])
            axes[idx].set_yticks(range(len(top_features)))
            axes[idx].set_yticklabels(top_features['feature'])
            axes[idx].set_title(f'{model_name}')
            axes[idx].set_xlabel('Importance')
            axes[idx].invert_yaxis()

        plt.tight_layout()
        importance_file = results_dir / f"feature_importance_{target_name}.png"
        plt.savefig(importance_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✓ Feature importance plot saved to {importance_file}")

def print_summary(all_results):
    """Print comprehensive results summary"""
    print(f"\n{'='*80}")
    print("TREE-BASED MODELS RESULTS SUMMARY")
    print(f"{'='*80}")

    for target_name, target_results in all_results.items():
        print(f"\n{target_name.upper()} PREDICTION RESULTS:")
        print("-" * 50)

        # Find best model for each metric
        best_rmse = min(target_results.items(), key=lambda x: x[1]['test_metrics']['rmse'])
        best_r2 = max(target_results.items(), key=lambda x: x[1]['test_metrics']['r2'])
        best_mape = min(target_results.items(), key=lambda x: x[1]['test_metrics']['mape'])
        best_dir_acc = max(target_results.items(), key=lambda x: x[1]['test_metrics']['directional_accuracy'])

        for model_name, model_data in target_results.items():
            metrics = model_data['test_metrics']
            print(f"\n{model_name}:")
            print(f"  RMSE: {metrics['rmse']:.6f}")
            print(f"  R²: {metrics['r2']:.4f}")
            print(f"  MAPE: {metrics['mape']:.2f}%")
            print(f"  Directional Accuracy: {metrics['directional_accuracy']:.2f}%")

        print(f"\nBest Models for {target_name}:")
        print(f"  Best RMSE: {best_rmse[0]} ({best_rmse[1]['test_metrics']['rmse']:.6f})")
        print(f"  Best R²: {best_r2[0]} ({best_r2[1]['test_metrics']['r2']:.4f})")
        print(f"  Best MAPE: {best_mape[0]} ({best_mape[1]['test_metrics']['mape']:.2f}%)")
        print(f"  Best Directional Accuracy: {best_dir_acc[0]} ({best_dir_acc[1]['test_metrics']['directional_accuracy']:.2f}%)")

def main():
    """Main execution function"""
    print("Bitcoin Price Prediction - Tree-based Models")
    print("=" * 60)

    # Load data
    data, simple_split = load_data()
    feature_cols, target_cols = prepare_features_targets(data)

    # Prepare training data
    X_train = simple_split['train'][feature_cols]
    X_test = simple_split['test'][feature_cols]

    # Results storage
    all_results = {}

    # Train models for each target
    for target_name in target_cols:
        print(f"\n{'#'*60}")
        print(f"TRAINING MODELS FOR {target_name.upper()}")
        print(f"{'#'*60}")

        y_train = simple_split['train'][target_name]
        y_test = simple_split['test'][target_name]

        # Remove NaN values
        train_mask = ~(X_train.isna().any(axis=1) | y_train.isna())
        test_mask = ~(X_test.isna().any(axis=1) | y_test.isna())

        X_train_clean = X_train[train_mask]
        y_train_clean = y_train[train_mask]
        X_test_clean = X_test[test_mask]
        y_test_clean = y_test[test_mask]

        print(f"Clean data - Train: {X_train_clean.shape}, Test: {X_test_clean.shape}")

        # Train Random Forest
        rf_results = train_random_forest(X_train_clean, y_train_clean, X_test_clean, y_test_clean, target_name)

        # Train XGBoost
        xgb_results = train_xgboost(X_train_clean, y_train_clean, X_test_clean, y_test_clean, target_name)

        # Train LightGBM
        lgb_results = train_lightgbm(X_train_clean, y_train_clean, X_test_clean, y_test_clean, target_name)

        # Store results
        all_results[target_name] = {
            'RandomForest': rf_results,
            'XGBoost': xgb_results,
            'LightGBM': lgb_results
        }

    # Save models and results
    export_results = save_models_and_results(all_results)

    # Create visualizations
    create_visualizations(all_results)

    # Print summary
    print_summary(all_results)

    print(f"\n{'='*60}")
    print("TREE-BASED MODELS TRAINING COMPLETED!")
    print(f"{'='*60}")
    print("✓ Models saved to: models/")
    print("✓ Results saved to: results/tree_models_results.json")
    print("✓ Visualizations saved to: results/")

    return all_results

if __name__ == "__main__":
    results = main()
