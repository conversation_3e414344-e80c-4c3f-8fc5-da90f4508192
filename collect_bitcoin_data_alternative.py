"""Alternative Bitcoin data collection using multiple sources."""

import requests
import pandas as pd
import time
from datetime import datetime, timedelta
import os
import json

def collect_from_coingecko():
    """Collect Bitcoin data from CoinGecko API (free tier)."""
    print("Trying CoinGecko API...")
    
    try:
        # CoinGecko free API - get last 365 days of data
        url = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
        params = {
            'vs_currency': 'usd',
            'days': '365',
            'interval': 'daily'
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # Convert to DataFrame
            prices = data['prices']
            volumes = data['total_volumes']
            
            df_data = []
            for i, (timestamp, price) in enumerate(prices):
                date = pd.to_datetime(timestamp, unit='ms')
                volume = volumes[i][1] if i < len(volumes) else 0
                
                df_data.append({
                    'Date': date,
                    'close': price,
                    'volume': volume
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('Date', inplace=True)
            
            # Add basic OHLC (simplified - using close price)
            df['open'] = df['close'].shift(1).fillna(df['close'])
            df['high'] = df['close'] * 1.02  # Approximate
            df['low'] = df['close'] * 0.98   # Approximate
            df['adj close'] = df['close']
            
            return df
            
        else:
            print(f"CoinGecko API error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"CoinGecko error: {e}")
        return None

def collect_from_binance():
    """Collect Bitcoin data from Binance API (no auth required)."""
    print("Trying Binance API...")
    
    try:
        # Binance API - get daily klines
        url = "https://api.binance.com/api/v3/klines"
        params = {
            'symbol': 'BTCUSDT',
            'interval': '1d',
            'limit': 365  # Last 365 days
        }
        
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            df_data = []
            for kline in data:
                df_data.append({
                    'Date': pd.to_datetime(int(kline[0]), unit='ms'),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('Date', inplace=True)
            df['adj close'] = df['close']
            
            return df
            
        else:
            print(f"Binance API error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Binance error: {e}")
        return None

def collect_bitcoin_data_alternative():
    """Try multiple data sources."""
    print("🔄 Collecting Bitcoin data from alternative sources...")
    
    # Create data directory
    os.makedirs('data/raw', exist_ok=True)
    
    # Try different sources
    sources = [
        ("Binance", collect_from_binance),
        ("CoinGecko", collect_from_coingecko)
    ]
    
    for source_name, collect_func in sources:
        print(f"\n📡 Trying {source_name}...")
        time.sleep(2)  # Rate limiting
        
        data = collect_func()
        
        if data is not None and not data.empty:
            # Save data
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'data/raw/BTC_USD_{source_name.lower()}_{timestamp}.csv'
            data.to_csv(filename)
            
            print(f"✅ Successfully collected {len(data)} records from {source_name}")
            print(f"📅 Date range: {data.index.min()} to {data.index.max()}")
            print(f"💾 Data saved to: {filename}")
            
            # Display basic statistics
            print(f"\n📊 Price Statistics:")
            print(f"Current price: ${data['close'].iloc[-1]:,.2f}")
            print(f"Min price: ${data['close'].min():,.2f}")
            print(f"Max price: ${data['close'].max():,.2f}")
            print(f"Average price: ${data['close'].mean():,.2f}")
            
            return data
    
    print("❌ All data sources failed. Please try again later.")
    return None

if __name__ == "__main__":
    collect_bitcoin_data_alternative()
