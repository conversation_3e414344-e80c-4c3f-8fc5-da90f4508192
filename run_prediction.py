#!/usr/bin/env python3
"""
Bitcoin Price Prediction - Simple Command Line Runner
This script runs the core prediction functionality without <PERSON><PERSON><PERSON>.
"""

import pandas as pd
import numpy as np
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def main():
    print("🚀 Bitcoin Price Prediction System")
    print("=" * 50)
    
    # 1. Load Data
    print("\n📊 Loading data...")
    try:
        data = pd.read_csv('data/processed/bitcoin_preprocessed.csv')
        print(f"✅ Data loaded successfully: {data.shape[0]} rows, {data.shape[1]} columns")
        
        # Convert date column
        if 'Date' in data.columns:
            data['Date'] = pd.to_datetime(data['Date'])
            data = data.set_index('Date')
        
        print(f"📅 Data range: {data.index.min()} to {data.index.max()}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return
    
    # 2. Load Models
    print("\n🤖 Loading trained models...")
    models = {}
    model_files = [
        'randomforest_target_return_1d_model.joblib',
        'randomforest_target_return_7d_model.joblib', 
        'randomforest_target_return_30d_model.joblib',
        'xgboost_target_return_1d_model.joblib',
        'xgboost_target_return_7d_model.joblib',
        'xgboost_target_return_30d_model.joblib',
        'lightgbm_target_return_1d_model.joblib',
        'lightgbm_target_return_7d_model.joblib',
        'lightgbm_target_return_30d_model.joblib'
    ]
    
    for model_file in model_files:
        try:
            model_path = f'models/{model_file}'
            model = joblib.load(model_path)
            model_name = model_file.replace('_model.joblib', '')
            models[model_name] = model
            print(f"✅ Loaded {model_name}")
        except Exception as e:
            print(f"⚠️ Could not load {model_file}: {e}")
    
    # Load scaler
    try:
        scaler = joblib.load('models/scaler_standard.joblib')
        print("✅ Loaded scaler")
    except Exception as e:
        print(f"⚠️ Could not load scaler: {e}")
        scaler = None
    
    # 3. Prepare Recent Data for Prediction
    print("\n🔮 Making predictions...")

    # Get the most recent data point
    recent_data = data.tail(1).copy()

    # Load a model to get the exact expected features
    try:
        # Load XGBoost model to get expected features (most reliable)
        sample_model = joblib.load('models/xgboost_target_return_1d_model.joblib')
        if hasattr(sample_model, 'feature_names_in_'):
            expected_features = sample_model.feature_names_in_.tolist()
            print(f"📊 Model expects: {len(expected_features)} features")

            # Select only the features that the models expect
            X_recent = recent_data[expected_features]

            print(f"📈 Using {len(expected_features)} features for prediction")
            print(f"🕐 Most recent data point: {recent_data.index[0]}")

        else:
            raise Exception("Model doesn't have feature_names_in_ attribute")

    except Exception as e:
        print(f"❌ Error loading model features: {e}")
        # Fallback: exclude OHLCV and target columns
        exclude_cols = ['open', 'high', 'low', 'close', 'volume'] + [col for col in recent_data.columns if col.startswith('target_')]
        feature_cols = [col for col in recent_data.columns if col not in exclude_cols]
        X_recent = recent_data[feature_cols]
        print(f"📈 Using {len(feature_cols)} features for prediction (fallback)")
        print(f"🕐 Most recent data point: {recent_data.index[0]}")
    
    # 4. Generate Predictions
    predictions = {}
    
    for model_name, model in models.items():
        try:
            # Make prediction
            pred = model.predict(X_recent)[0]
            predictions[model_name] = pred
            
            # Extract horizon and model type
            parts = model_name.split('_')
            horizon = parts[-1]  # 1d, 7d, 30d
            model_type = '_'.join(parts[:-3])  # randomforest, xgboost, lightgbm
            
            print(f"📊 {model_type.upper()} ({horizon}): {pred:.4f}")
            
        except Exception as e:
            print(f"❌ Error with {model_name}: {e}")
    
    # 5. Calculate Ensemble Predictions
    print("\n🎯 Ensemble Predictions:")
    
    horizons = ['1d', '7d', '30d']
    ensemble_preds = {}
    
    for horizon in horizons:
        horizon_preds = [pred for name, pred in predictions.items() if name.endswith(horizon)]
        if horizon_preds:
            ensemble_pred = np.mean(horizon_preds)
            ensemble_preds[horizon] = ensemble_pred
            print(f"🔮 {horizon} return prediction: {ensemble_pred:.4f} ({ensemble_pred*100:.2f}%)")
    
    # 6. Show Recent Price Info
    print("\n💰 Recent Bitcoin Price Info:")
    if 'Close' in data.columns:
        recent_price = data['Close'].iloc[-1]
        print(f"📈 Latest Close Price: ${recent_price:.2f}")
        
        # Calculate predicted prices based on returns
        for horizon, return_pred in ensemble_preds.items():
            predicted_price = recent_price * (1 + return_pred)
            print(f"🎯 Predicted price in {horizon}: ${predicted_price:.2f}")
    
    # 7. Basic Visualization
    print("\n📊 Creating basic visualization...")
    try:
        plt.figure(figsize=(12, 8))
        
        # Plot recent price history
        plt.subplot(2, 1, 1)
        recent_history = data['Close'].tail(30)
        plt.plot(recent_history.index, recent_history.values, 'b-', linewidth=2)
        plt.title('Bitcoin Price - Last 30 Days')
        plt.ylabel('Price ($)')
        plt.grid(True, alpha=0.3)
        
        # Plot prediction comparison
        plt.subplot(2, 1, 2)
        horizons_plot = list(ensemble_preds.keys())
        returns_plot = [ensemble_preds[h]*100 for h in horizons_plot]
        
        bars = plt.bar(horizons_plot, returns_plot, 
                      color=['green' if r > 0 else 'red' for r in returns_plot],
                      alpha=0.7)
        plt.title('Predicted Returns by Horizon')
        plt.ylabel('Return (%)')
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, returns_plot):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                    f'{value:.2f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('bitcoin_predictions.png', dpi=300, bbox_inches='tight')
        print("✅ Visualization saved as 'bitcoin_predictions.png'")
        
        # Show plot if possible
        try:
            plt.show()
        except:
            print("📊 Plot saved to file (display not available)")
            
    except Exception as e:
        print(f"⚠️ Could not create visualization: {e}")
    
    print("\n🎉 Prediction complete!")
    print("=" * 50)

if __name__ == "__main__":
    main()
