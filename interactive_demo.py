"""
Interactive Demo Component for Bitcoin Price Prediction Presentation
Real-time prediction visualization and user interaction
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class BitcoinPredictionDemo:
    def __init__(self):
        """Initialize the demo with models and data"""
        self.models = {}
        self.scaler = None
        self.data = None
        self.load_components()
    
    def load_components(self):
        """Load all necessary components"""
        print("🔄 Loading demo components...")
        
        try:
            # Load data
            self.data = pd.read_csv('data/processed/bitcoin_preprocessed.csv', 
                                  index_col=0, parse_dates=True)
            print(f"✅ Data loaded: {self.data.shape}")
            
            # Load models
            model_files = {
                'rf_1d': 'models/randomforest_target_return_1d_model.joblib',
                'rf_7d': 'models/randomforest_target_return_7d_model.joblib',
                'rf_30d': 'models/randomforest_target_return_30d_model.joblib',
                'xgb_1d': 'models/xgboost_target_return_1d_model.joblib',
                'xgb_7d': 'models/xgboost_target_return_7d_model.joblib',
                'xgb_30d': 'models/xgboost_target_return_30d_model.joblib',
                'lgb_1d': 'models/lightgbm_target_return_1d_model.joblib',
                'lgb_7d': 'models/lightgbm_target_return_7d_model.joblib',
                'lgb_30d': 'models/lightgbm_target_return_30d_model.joblib'
            }
            
            for name, path in model_files.items():
                try:
                    self.models[name] = joblib.load(path)
                    print(f"✅ Loaded {name}")
                except Exception as e:
                    print(f"⚠️ Could not load {name}: {e}")
            
            # Load scaler
            self.scaler = joblib.load('models/scaler_standard.joblib')
            print("✅ Scaler loaded")
            
        except Exception as e:
            print(f"❌ Error loading components: {e}")
    
    def get_current_market_data(self):
        """Get current market data for display"""
        latest = self.data.iloc[-1]
        
        market_data = {
            'current_price': latest['close'],
            'daily_change': latest['daily_return'] * 100,
            'volatility_7d': latest.get('volatility_7d', 0) * 100,
            'rsi_14': latest.get('rsi_14', 50),
            'volume': latest.get('volume', 0),
            'date': self.data.index[-1].strftime('%Y-%m-%d')
        }
        
        return market_data
    
    def make_live_predictions(self):
        """Make live predictions for all horizons"""
        print("\n🔮 Making Live Predictions...")
        print("=" * 50)
        
        # Get latest features
        latest_data = self.data.tail(1)
        
        # Prepare features
        exclude_cols = ['target_return_1d', 'target_return_7d', 'target_return_30d',
                       'open', 'high', 'low', 'close', 'adj_close', 'volume']
        feature_cols = [col for col in latest_data.columns if col not in exclude_cols]
        X_latest = latest_data[feature_cols]
        
        # Scale features
        X_scaled = self.scaler.transform(X_latest)
        
        # Current price
        current_price = latest_data['close'].iloc[0]
        
        # Make predictions for each horizon
        horizons = ['1d', '7d', '30d']
        algorithms = ['rf', 'xgb', 'lgb']
        
        predictions = {}
        ensemble_predictions = {}
        
        for horizon in horizons:
            horizon_preds = []
            individual_preds = {}
            
            for algo in algorithms:
                model_key = f"{algo}_{horizon}"
                if model_key in self.models:
                    try:
                        pred = self.models[model_key].predict(X_scaled)[0]
                        horizon_preds.append(pred)
                        individual_preds[algo.upper()] = pred
                    except Exception as e:
                        print(f"⚠️ {model_key} prediction failed: {e}")
                        individual_preds[algo.upper()] = 0.0
            
            # Calculate ensemble
            if horizon_preds:
                ensemble_pred = np.mean(horizon_preds)
                ensemble_predictions[horizon] = ensemble_pred
            else:
                ensemble_predictions[horizon] = 0.0
            
            predictions[horizon] = individual_preds
        
        # Calculate price targets
        price_targets = {}
        for horizon, return_pred in ensemble_predictions.items():
            price_targets[horizon] = current_price * (1 + return_pred)
        
        return {
            'current_price': current_price,
            'predictions': predictions,
            'ensemble_predictions': ensemble_predictions,
            'price_targets': price_targets
        }
    
    def create_prediction_visualization(self, results):
        """Create interactive prediction visualization"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Plot 1: Current vs Predicted Prices
        horizons = ['1d', '7d', '30d']
        current_prices = [results['current_price']] * 3
        predicted_prices = [results['price_targets'][h] for h in horizons]
        
        x = np.arange(len(horizons))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, current_prices, width, label='Current Price', alpha=0.8, color='blue')
        bars2 = ax1.bar(x + width/2, predicted_prices, width, label='Predicted Price', alpha=0.8, color='red')
        
        ax1.set_xlabel('Time Horizon')
        ax1.set_ylabel('Price (USD)')
        ax1.set_title('Current vs Predicted Bitcoin Prices', fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(horizons)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Add value labels on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'${height:,.0f}', ha='center', va='bottom')
        
        # Plot 2: Return Predictions by Model
        models = ['RF', 'XGB', 'LGB']
        returns_1d = [results['predictions']['1d'].get(m, 0) * 100 for m in models]
        returns_7d = [results['predictions']['7d'].get(m, 0) * 100 for m in models]
        returns_30d = [results['predictions']['30d'].get(m, 0) * 100 for m in models]
        
        x = np.arange(len(models))
        width = 0.25
        
        ax2.bar(x - width, returns_1d, width, label='1-Day', alpha=0.8)
        ax2.bar(x, returns_7d, width, label='7-Day', alpha=0.8)
        ax2.bar(x + width, returns_30d, width, label='30-Day', alpha=0.8)
        
        ax2.set_xlabel('Model')
        ax2.set_ylabel('Predicted Return (%)')
        ax2.set_title('Return Predictions by Model and Horizon', fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(models)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # Plot 3: Ensemble Predictions
        ensemble_returns = [results['ensemble_predictions'][h] * 100 for h in horizons]
        colors = ['red' if r < 0 else 'green' for r in ensemble_returns]
        
        bars = ax3.bar(horizons, ensemble_returns, color=colors, alpha=0.7)
        ax3.set_xlabel('Time Horizon')
        ax3.set_ylabel('Ensemble Return Prediction (%)')
        ax3.set_title('Ensemble Return Predictions', fontweight='bold')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # Add value labels
        for bar, value in zip(bars, ensemble_returns):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:+.2f}%', ha='center', 
                    va='bottom' if height > 0 else 'top')
        
        # Plot 4: Market Sentiment Gauge
        avg_return = np.mean(ensemble_returns)
        sentiment_score = max(-100, min(100, avg_return * 10))  # Scale to -100 to 100
        
        # Create a gauge-like visualization
        theta = np.linspace(0, np.pi, 100)
        r = np.ones_like(theta)
        
        ax4 = plt.subplot(224, projection='polar')
        ax4.plot(theta, r, 'k-', linewidth=3)
        ax4.fill_between(theta, 0, r, alpha=0.3, color='lightgray')
        
        # Add sentiment indicator
        sentiment_angle = np.pi * (50 + sentiment_score) / 100
        ax4.plot([sentiment_angle, sentiment_angle], [0, 1], 'r-', linewidth=5)
        
        ax4.set_ylim(0, 1.2)
        ax4.set_theta_zero_location('W')
        ax4.set_theta_direction(1)
        ax4.set_thetagrids([0, 45, 90, 135, 180], ['Bullish', 'Moderate+', 'Neutral', 'Moderate-', 'Bearish'])
        ax4.set_title('Market Sentiment Gauge', fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig('presentation/live_prediction_demo.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def display_market_summary(self, market_data, results):
        """Display comprehensive market summary"""
        print("\n" + "="*60)
        print("📊 LIVE BITCOIN MARKET ANALYSIS")
        print("="*60)
        
        print(f"\n💰 Current Market Data:")
        print(f"   Price: ${market_data['current_price']:,.2f}")
        print(f"   Daily Change: {market_data['daily_change']:+.2f}%")
        print(f"   7-Day Volatility: {market_data['volatility_7d']:.2f}%")
        print(f"   RSI (14): {market_data['rsi_14']:.1f}")
        print(f"   Date: {market_data['date']}")
        
        print(f"\n🔮 Multi-Horizon Predictions:")
        for horizon in ['1d', '7d', '30d']:
            return_pred = results['ensemble_predictions'][horizon]
            price_target = results['price_targets'][horizon]
            trend = "📈 BULLISH" if return_pred > 0 else "📉 BEARISH"
            
            print(f"   {horizon:3}: {return_pred*100:+6.2f}% → ${price_target:8,.0f} {trend}")
        
        # Risk assessment
        avg_return = np.mean(list(results['ensemble_predictions'].values()))
        if avg_return > 0.02:
            risk = "HIGH BULLISH 🚀"
        elif avg_return > 0.01:
            risk = "MODERATE BULLISH 📈"
        elif avg_return > -0.01:
            risk = "NEUTRAL ⚖️"
        elif avg_return > -0.02:
            risk = "MODERATE BEARISH 📉"
        else:
            risk = "HIGH BEARISH 🔻"
        
        print(f"\n⚠️ Risk Assessment:")
        print(f"   Overall Sentiment: {risk}")
        print(f"   Average Return: {avg_return*100:+.2f}%")
        
        print("\n" + "="*60)
    
    def run_live_demo(self):
        """Run the complete live demonstration"""
        print("🎬 Starting Live Bitcoin Prediction Demo")
        print("="*50)
        
        # Get market data
        market_data = self.get_current_market_data()
        
        # Make predictions
        results = self.make_live_predictions()
        
        # Display summary
        self.display_market_summary(market_data, results)
        
        # Create visualization
        self.create_prediction_visualization(results)
        
        print("\n✅ Live demo completed successfully!")
        return results

def main():
    """Main demo function"""
    demo = BitcoinPredictionDemo()
    results = demo.run_live_demo()
    return results

if __name__ == "__main__":
    main()
