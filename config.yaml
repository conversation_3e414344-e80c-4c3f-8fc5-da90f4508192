# Bitcoin Price Prediction Configuration

# Data Collection Settings
data:
  # Primary data source
  primary_source: "yahoo"  # yahoo, coingecko, alphavantage
  
  # Date range for historical data
  start_date: "2019-01-01"  # At least 5 years of data
  end_date: null  # null for current date
  
  # Data frequency
  frequency: "1d"  # 1d for daily, 1h for hourly
  
  # Symbols to collect
  symbols:
    - "BTC-USD"
  
  # API settings
  apis:
    yahoo:
      rate_limit: 2000  # requests per hour
    coingecko:
      rate_limit: 50    # requests per minute (free tier)
      base_url: "https://api.coingecko.com/api/v3"
    alphavantage:
      rate_limit: 5     # requests per minute (free tier)
      base_url: "https://www.alphavantage.co/query"

# Feature Engineering Settings
features:
  # Lag features
  price_lags: [1, 2, 3, 7, 14, 30]
  volume_lags: [1, 7, 30]
  
  # Moving averages
  moving_averages: [7, 14, 21, 30, 50, 100, 200]
  
  # Technical indicators
  technical_indicators:
    rsi:
      periods: [14, 21]
    bollinger_bands:
      periods: [20]
      std_dev: 2
    macd:
      fast_period: 12
      slow_period: 26
      signal_period: 9
    stochastic:
      k_period: 14
      d_period: 3
  
  # Volatility measures
  volatility:
    rolling_windows: [7, 14, 30]
    garch_params:
      p: 1
      q: 1

# Model Settings
models:
  # ARIMA settings
  arima:
    max_p: 5
    max_d: 2
    max_q: 5
    seasonal: true
    m: 7  # weekly seasonality
    
  # Random Forest settings
  random_forest:
    n_estimators: 100
    max_depth: 10
    min_samples_split: 5
    random_state: 42
    
  # XGBoost settings
  xgboost:
    n_estimators: 100
    max_depth: 6
    learning_rate: 0.1
    subsample: 0.8
    random_state: 42
    
  # LSTM settings
  lstm:
    sequence_length: 60
    hidden_units: 50
    dropout: 0.2
    epochs: 100
    batch_size: 32
    learning_rate: 0.001

# Evaluation Settings
evaluation:
  # Train-test split
  test_size: 0.2
  validation_size: 0.1
  
  # Cross-validation
  cv_folds: 5
  cv_method: "time_series_split"  # time_series_split, walk_forward
  
  # Metrics
  metrics:
    - "mape"
    - "rmse"
    - "mae"
    - "directional_accuracy"
    - "sharpe_ratio"
    - "max_drawdown"

# Trading Simulation Settings
trading:
  initial_capital: 10000
  transaction_cost: 0.001  # 0.1%
  position_sizing: "fixed"  # fixed, kelly, volatility_target
  risk_free_rate: 0.02  # 2% annual

# Paths
paths:
  data_raw: "data/raw"
  data_processed: "data/processed"
  data_features: "data/features"
  models: "models"
  results: "results"
  logs: "logs"

# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/bitcoin_prediction.log"
