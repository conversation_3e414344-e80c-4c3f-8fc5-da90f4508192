"""ARIMA Family Models for Bitcoin Price Prediction."""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Statistical modeling libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf

# Auto ARIMA
try:
    from pmdarima import auto_arima
    AUTO_ARIMA_AVAILABLE = True
except ImportError:
    AUTO_ARIMA_AVAILABLE = False
    print("Warning: pmdarima not available. Auto-ARIMA will be skipped.")

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import joblib
import json

def load_data_for_arima():
    """Load data specifically for ARIMA modeling."""
    # Load unscaled data (ARIMA works better with original scale)
    data_path = Path('data/processed/bitcoin_preprocessed_unscaled.csv')
    if not data_path.exists():
        raise FileNotFoundError("Unscaled preprocessed data not found.")
    
    df = pd.read_csv(data_path, index_col=0, parse_dates=True)
    print(f"Loaded data shape: {df.shape}")
    
    # For ARIMA, we'll focus on the close price and returns
    return df

def check_stationarity(series, name="Series"):
    """Check stationarity using ADF and KPSS tests."""
    print(f"\n" + "="*50)
    print(f"STATIONARITY TESTS FOR {name.upper()}")
    print("="*50)
    
    # Augmented Dickey-Fuller test
    adf_result = adfuller(series.dropna())
    print("Augmented Dickey-Fuller Test:")
    print(f"ADF Statistic: {adf_result[0]:.6f}")
    print(f"p-value: {adf_result[1]:.6f}")
    print(f"Critical Values:")
    for key, value in adf_result[4].items():
        print(f"\t{key}: {value:.3f}")
    
    adf_stationary = adf_result[1] <= 0.05
    print(f"ADF Test Result: {'Stationary' if adf_stationary else 'Non-stationary'}")
    
    # KPSS test
    kpss_result = kpss(series.dropna(), regression='c')
    print(f"\nKPSS Test:")
    print(f"KPSS Statistic: {kpss_result[0]:.6f}")
    print(f"p-value: {kpss_result[1]:.6f}")
    print(f"Critical Values:")
    for key, value in kpss_result[3].items():
        print(f"\t{key}: {value:.3f}")
    
    kpss_stationary = kpss_result[1] > 0.05
    print(f"KPSS Test Result: {'Stationary' if kpss_stationary else 'Non-stationary'}")
    
    # Combined result
    both_agree = adf_stationary and kpss_stationary
    print(f"\nOverall Assessment: {'Stationary' if both_agree else 'Non-stationary'}")
    
    return both_agree

def plot_series_analysis(series, name="Bitcoin Price"):
    """Plot series analysis including ACF and PACF."""
    print(f"\nCreating plots for {name}...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Time series plot
    axes[0, 0].plot(series.index, series.values)
    axes[0, 0].set_title(f'{name} Time Series')
    axes[0, 0].set_xlabel('Date')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].grid(True, alpha=0.3)
    
    # ACF plot
    plot_acf(series.dropna(), ax=axes[0, 1], lags=40, alpha=0.05)
    axes[0, 1].set_title('Autocorrelation Function (ACF)')
    
    # PACF plot
    plot_pacf(series.dropna(), ax=axes[1, 0], lags=40, alpha=0.05)
    axes[1, 0].set_title('Partial Autocorrelation Function (PACF)')
    
    # Distribution
    axes[1, 1].hist(series.dropna(), bins=50, alpha=0.7, density=True)
    axes[1, 1].set_title('Distribution')
    axes[1, 1].set_xlabel('Value')
    axes[1, 1].set_ylabel('Density')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    filename = f'results/arima_{name.lower().replace(" ", "_")}_analysis.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Analysis plots saved to: {filename}")

def find_optimal_arima_order(series, max_p=5, max_d=2, max_q=5):
    """Find optimal ARIMA order using grid search."""
    print(f"\n" + "="*50)
    print("ARIMA ORDER SELECTION")
    print("="*50)
    
    best_aic = np.inf
    best_order = None
    best_model = None
    results = []
    
    print("Testing ARIMA orders...")
    for p in range(max_p + 1):
        for d in range(max_d + 1):
            for q in range(max_q + 1):
                try:
                    model = ARIMA(series, order=(p, d, q))
                    fitted_model = model.fit()
                    
                    aic = fitted_model.aic
                    bic = fitted_model.bic
                    
                    results.append({
                        'order': (p, d, q),
                        'aic': aic,
                        'bic': bic,
                        'params': fitted_model.params.to_dict()
                    })
                    
                    if aic < best_aic:
                        best_aic = aic
                        best_order = (p, d, q)
                        best_model = fitted_model
                    
                    print(f"ARIMA{(p,d,q)}: AIC={aic:.2f}, BIC={bic:.2f}")
                    
                except Exception as e:
                    print(f"ARIMA{(p,d,q)}: Failed - {str(e)[:50]}")
                    continue
    
    print(f"\nBest ARIMA order: {best_order}")
    print(f"Best AIC: {best_aic:.2f}")
    
    return best_model, best_order, results

def auto_arima_selection(series):
    """Use auto-ARIMA for automatic order selection."""
    if not AUTO_ARIMA_AVAILABLE:
        print("Auto-ARIMA not available. Skipping...")
        return None, None
    
    print(f"\n" + "="*50)
    print("AUTO-ARIMA SELECTION")
    print("="*50)
    
    try:
        auto_model = auto_arima(
            series,
            start_p=0, start_q=0,
            max_p=5, max_q=5,
            seasonal=False,
            stepwise=True,
            suppress_warnings=True,
            error_action='ignore',
            trace=True
        )
        
        print(f"Auto-ARIMA selected order: {auto_model.order}")
        print(f"AIC: {auto_model.aic():.2f}")
        
        return auto_model, auto_model.order
        
    except Exception as e:
        print(f"Auto-ARIMA failed: {e}")
        return None, None

def evaluate_arima_model(model, series, order):
    """Evaluate ARIMA model performance."""
    print(f"\n" + "="*50)
    print(f"EVALUATING ARIMA{order} MODEL")
    print("="*50)
    
    # Model summary
    print("Model Summary:")
    print(model.summary())
    
    # Residual analysis
    residuals = model.resid
    
    # Ljung-Box test for residual autocorrelation
    lb_test = acorr_ljungbox(residuals, lags=10, return_df=True)
    print(f"\nLjung-Box Test (p-values):")
    print(lb_test['lb_pvalue'])
    
    # Residual statistics
    print(f"\nResidual Statistics:")
    print(f"Mean: {residuals.mean():.6f}")
    print(f"Std: {residuals.std():.6f}")
    print(f"Skewness: {residuals.skew():.6f}")
    print(f"Kurtosis: {residuals.kurtosis():.6f}")
    
    # Plot residuals
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Residuals over time
    axes[0, 0].plot(residuals.index, residuals.values)
    axes[0, 0].set_title('Residuals Over Time')
    axes[0, 0].set_xlabel('Date')
    axes[0, 0].set_ylabel('Residuals')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Residuals distribution
    axes[0, 1].hist(residuals, bins=50, alpha=0.7, density=True)
    axes[0, 1].set_title('Residuals Distribution')
    axes[0, 1].set_xlabel('Residuals')
    axes[0, 1].set_ylabel('Density')
    axes[0, 1].grid(True, alpha=0.3)
    
    # ACF of residuals
    plot_acf(residuals, ax=axes[1, 0], lags=40, alpha=0.05)
    axes[1, 0].set_title('ACF of Residuals')
    
    # Q-Q plot
    from scipy import stats
    stats.probplot(residuals, dist="norm", plot=axes[1, 1])
    axes[1, 1].set_title('Q-Q Plot of Residuals')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'results/arima_{order}_residual_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Residual analysis saved to: results/arima_{order}_residual_analysis.png")
    
    return {
        'aic': model.aic,
        'bic': model.bic,
        'ljung_box_pvalues': lb_test['lb_pvalue'].to_dict(),
        'residual_stats': {
            'mean': residuals.mean(),
            'std': residuals.std(),
            'skewness': residuals.skew(),
            'kurtosis': residuals.kurtosis()
        }
    }

def forecast_with_arima(model, steps=30):
    """Generate forecasts using ARIMA model."""
    print(f"\n" + "="*50)
    print(f"GENERATING {steps}-STEP FORECAST")
    print("="*50)
    
    # Generate forecast
    forecast = model.forecast(steps=steps)
    conf_int = model.get_forecast(steps=steps).conf_int()
    
    print(f"Forecast for next {steps} periods:")
    for i, (pred, lower, upper) in enumerate(zip(forecast, conf_int.iloc[:, 0], conf_int.iloc[:, 1])):
        print(f"Step {i+1}: {pred:.2f} [{lower:.2f}, {upper:.2f}]")
    
    return forecast, conf_int

def train_arima_models(df):
    """Train multiple ARIMA models on Bitcoin data."""
    print("Bitcoin Price Prediction - ARIMA Models")
    print("=" * 60)
    
    # Create results directory
    Path('results').mkdir(exist_ok=True)
    Path('models').mkdir(exist_ok=True)
    
    results = {}
    
    # 1. Analyze Bitcoin price (levels)
    price_series = df['close']
    print("\n1. ANALYZING BITCOIN PRICE (LEVELS)")
    is_stationary = check_stationarity(price_series, "Bitcoin Price")
    plot_series_analysis(price_series, "Bitcoin Price")
    
    # 2. Analyze Bitcoin returns
    returns_series = df['daily_return'].dropna()
    print("\n2. ANALYZING BITCOIN RETURNS")
    is_returns_stationary = check_stationarity(returns_series, "Bitcoin Returns")
    plot_series_analysis(returns_series, "Bitcoin Returns")
    
    # 3. Train ARIMA on price levels
    if not is_stationary:
        print("\n3. TRAINING ARIMA ON PRICE LEVELS (with differencing)")
        price_model, price_order, price_results = find_optimal_arima_order(price_series)
        if price_model:
            price_evaluation = evaluate_arima_model(price_model, price_series, price_order)
            price_forecast, price_conf_int = forecast_with_arima(price_model, steps=30)
            
            # Save model
            joblib.dump(price_model, 'models/arima_price_model.joblib')
            results['price_model'] = {
                'order': price_order,
                'evaluation': price_evaluation,
                'forecast': price_forecast.tolist(),
                'confidence_intervals': price_conf_int.values.tolist()
            }
    
    # 4. Train ARIMA on returns
    print("\n4. TRAINING ARIMA ON RETURNS")
    returns_model, returns_order, returns_results = find_optimal_arima_order(returns_series)
    if returns_model:
        returns_evaluation = evaluate_arima_model(returns_model, returns_series, returns_order)
        returns_forecast, returns_conf_int = forecast_with_arima(returns_model, steps=30)
        
        # Save model
        joblib.dump(returns_model, 'models/arima_returns_model.joblib')
        results['returns_model'] = {
            'order': returns_order,
            'evaluation': returns_evaluation,
            'forecast': returns_forecast.tolist(),
            'confidence_intervals': returns_conf_int.values.tolist()
        }
    
    # 5. Auto-ARIMA (if available)
    if AUTO_ARIMA_AVAILABLE:
        print("\n5. AUTO-ARIMA ON RETURNS")
        auto_model, auto_order = auto_arima_selection(returns_series)
        if auto_model:
            # Convert to statsmodels format for consistency
            auto_statsmodels = ARIMA(returns_series, order=auto_order).fit()
            auto_evaluation = evaluate_arima_model(auto_statsmodels, returns_series, auto_order)
            auto_forecast, auto_conf_int = forecast_with_arima(auto_statsmodels, steps=30)
            
            # Save model
            joblib.dump(auto_statsmodels, 'models/auto_arima_model.joblib')
            results['auto_arima_model'] = {
                'order': auto_order,
                'evaluation': auto_evaluation,
                'forecast': auto_forecast.tolist(),
                'confidence_intervals': auto_conf_int.values.tolist()
            }
    
    # Save all results
    with open('results/arima_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n" + "="*60)
    print("ARIMA MODELING COMPLETED!")
    print("="*60)
    print(f"Models trained: {len(results)}")
    print("✓ Models saved to: models/")
    print("✓ Results saved to: results/arima_results.json")
    
    return results

def main():
    """Main ARIMA modeling function."""
    try:
        # Load data
        df = load_data_for_arima()
        
        # Train ARIMA models
        results = train_arima_models(df)
        
        return results
        
    except Exception as e:
        print(f"Error during ARIMA modeling: {e}")
        return None

if __name__ == "__main__":
    main()
