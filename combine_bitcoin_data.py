"""Combine Bitcoin data from multiple sources into a single comprehensive dataset."""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob

def load_yahoo_data():
    """Load the most recent Yahoo Finance data."""
    print("📊 Loading Yahoo Finance data...")
    
    try:
        # Find the most recent Yahoo file
        yahoo_files = glob.glob('data/raw/BTC_USD_yahoo_*.csv')
        if not yahoo_files:
            print("   ⚠️ No Yahoo Finance files found")
            return None
        
        latest_yahoo = max(yahoo_files, key=os.path.getctime)
        print(f"   📁 Loading: {latest_yahoo}")
        
        df = pd.read_csv(latest_yahoo)
        df['Date'] = pd.to_datetime(df['Date'])
        # Remove timezone info to avoid comparison issues
        if df['Date'].dt.tz is not None:
            df['Date'] = df['Date'].dt.tz_localize(None)
        df.set_index('Date', inplace=True)
        
        # Clean column names
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]
        
        # Remove unnecessary columns
        cols_to_keep = ['open', 'high', 'low', 'close', 'adj_close', 'volume']
        df = df[[col for col in cols_to_keep if col in df.columns]]
        
        # Add source identifier
        df['source'] = 'yahoo'
        
        print(f"   ✅ Yahoo data: {len(df)} records from {df.index.min().date()} to {df.index.max().date()}")
        return df
        
    except Exception as e:
        print(f"   ❌ Error loading Yahoo data: {e}")
        return None

def load_binance_data():
    """Load the most recent Binance data."""
    print("📊 Loading Binance data...")
    
    try:
        # Find the most recent Binance file
        binance_files = glob.glob('data/raw/BTC_USD_binance_*.csv')
        if not binance_files:
            print("   ⚠️ No Binance files found")
            return None
        
        latest_binance = max(binance_files, key=os.path.getctime)
        print(f"   📁 Loading: {latest_binance}")
        
        df = pd.read_csv(latest_binance)
        df['Date'] = pd.to_datetime(df['Date'])
        # Remove timezone info to avoid comparison issues
        if df['Date'].dt.tz is not None:
            df['Date'] = df['Date'].dt.tz_localize(None)
        df.set_index('Date', inplace=True)
        
        # Clean column names
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]
        
        # Ensure we have adj_close column
        if 'adj_close' not in df.columns and 'close' in df.columns:
            df['adj_close'] = df['close']
        
        # Remove unnecessary columns
        cols_to_keep = ['open', 'high', 'low', 'close', 'adj_close', 'volume']
        df = df[[col for col in cols_to_keep if col in df.columns]]
        
        # Add source identifier
        df['source'] = 'binance'
        
        print(f"   ✅ Binance data: {len(df)} records from {df.index.min().date()} to {df.index.max().date()}")
        return df
        
    except Exception as e:
        print(f"   ❌ Error loading Binance data: {e}")
        return None

def combine_datasets(yahoo_df, binance_df):
    """Intelligently combine Yahoo and Binance datasets."""
    print("🔄 Combining datasets...")
    
    # Start with the dataset that has more historical data
    if yahoo_df is not None and binance_df is not None:
        # Use Yahoo as base (more historical data), fill gaps with Binance
        print("   📈 Using Yahoo as base, filling with Binance data")
        
        # Find the overlap period
        yahoo_end = yahoo_df.index.max()
        binance_start = binance_df.index.min()
        binance_end = binance_df.index.max()
        
        print(f"   📅 Yahoo ends: {yahoo_end.date()}")
        print(f"   📅 Binance starts: {binance_start.date()}")
        print(f"   📅 Binance ends: {binance_end.date()}")
        
        # Strategy: Use Yahoo for historical data, Binance for recent data
        if binance_end > yahoo_end:
            # Get Binance data that's newer than Yahoo
            new_binance_data = binance_df[binance_df.index > yahoo_end]
            
            if len(new_binance_data) > 0:
                print(f"   ➕ Adding {len(new_binance_data)} new records from Binance")
                combined_df = pd.concat([yahoo_df, new_binance_data])
            else:
                print("   ℹ️ No new data to add from Binance")
                combined_df = yahoo_df.copy()
        else:
            combined_df = yahoo_df.copy()
            
        # Fill any gaps in the overlap period with Binance data
        overlap_start = max(yahoo_df.index.min(), binance_df.index.min())
        overlap_end = min(yahoo_df.index.max(), binance_df.index.max())
        
        if overlap_start <= overlap_end:
            overlap_period = combined_df[(combined_df.index >= overlap_start) & 
                                       (combined_df.index <= overlap_end)]
            
            # Check for missing dates in the overlap period
            full_date_range = pd.date_range(start=overlap_start, end=overlap_end, freq='D')
            missing_dates = full_date_range.difference(overlap_period.index)
            
            if len(missing_dates) > 0:
                print(f"   🔧 Filling {len(missing_dates)} missing dates with Binance data")
                for missing_date in missing_dates:
                    if missing_date in binance_df.index:
                        combined_df.loc[missing_date] = binance_df.loc[missing_date]
        
    elif yahoo_df is not None:
        print("   📊 Using only Yahoo data")
        combined_df = yahoo_df.copy()
    elif binance_df is not None:
        print("   📊 Using only Binance data")
        combined_df = binance_df.copy()
    else:
        print("   ❌ No data available from either source")
        return None
    
    # Sort by date and remove duplicates
    combined_df = combined_df.sort_index()
    combined_df = combined_df[~combined_df.index.duplicated(keep='last')]
    
    return combined_df

def validate_and_clean_data(df):
    """Validate and clean the combined dataset."""
    print("🧹 Validating and cleaning data...")
    
    original_length = len(df)
    
    # Remove rows with all NaN values
    df = df.dropna(how='all')
    
    # Check for essential columns
    essential_cols = ['open', 'high', 'low', 'close', 'volume']
    missing_essential = [col for col in essential_cols if col not in df.columns]
    
    if missing_essential:
        print(f"   ⚠️ Missing essential columns: {missing_essential}")
    
    # Basic data validation
    if 'close' in df.columns:
        # Remove rows where close price is 0 or negative
        invalid_prices = (df['close'] <= 0) | df['close'].isna()
        if invalid_prices.sum() > 0:
            print(f"   🔧 Removing {invalid_prices.sum()} rows with invalid prices")
            df = df[~invalid_prices]
    
    # Check for reasonable price ranges (Bitcoin should be > $100 and < $1M)
    if 'close' in df.columns:
        unreasonable_prices = (df['close'] < 100) | (df['close'] > 1000000)
        if unreasonable_prices.sum() > 0:
            print(f"   🔧 Removing {unreasonable_prices.sum()} rows with unreasonable prices")
            df = df[~unreasonable_prices]
    
    # Ensure adj_close exists
    if 'adj_close' not in df.columns and 'close' in df.columns:
        df['adj_close'] = df['close']
        print("   ➕ Added adj_close column")
    
    print(f"   ✅ Cleaned data: {len(df)} records (removed {original_length - len(df)} invalid records)")
    
    return df

def save_combined_data(df):
    """Save the combined dataset."""
    print("💾 Saving combined dataset...")
    
    # Create output directory
    os.makedirs('data/raw', exist_ok=True)
    
    # Save with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'data/raw/BTC_USD_combined_{timestamp}.csv'
    
    df.to_csv(filename)
    
    print(f"   ✅ Saved to: {filename}")
    
    # Also save as the main raw data file
    main_filename = 'data/raw/BTC_USD_combined_latest.csv'
    df.to_csv(main_filename)
    print(f"   ✅ Saved as: {main_filename}")
    
    return filename

def combine_bitcoin_data():
    """Main function to combine Bitcoin data from all sources."""
    print("🚀 Combining Bitcoin Data from Multiple Sources")
    print("=" * 60)
    
    # Load data from both sources
    yahoo_df = load_yahoo_data()
    binance_df = load_binance_data()
    
    # Combine the datasets
    combined_df = combine_datasets(yahoo_df, binance_df)
    
    if combined_df is None:
        print("❌ Failed to combine data")
        return None
    
    # Validate and clean
    combined_df = validate_and_clean_data(combined_df)
    
    # Save the result
    filename = save_combined_data(combined_df)
    
    # Display summary statistics
    print("\n📊 Combined Dataset Summary:")
    print("=" * 40)
    print(f"📅 Date range: {combined_df.index.min().date()} to {combined_df.index.max().date()}")
    print(f"📈 Total records: {len(combined_df)}")
    print(f"💰 Price range: ${combined_df['close'].min():,.2f} - ${combined_df['close'].max():,.2f}")
    print(f"📊 Current price: ${combined_df['close'].iloc[-1]:,.2f}")
    
    # Show data sources breakdown
    if 'source' in combined_df.columns:
        source_counts = combined_df['source'].value_counts()
        print(f"\n📡 Data sources:")
        for source, count in source_counts.items():
            print(f"   {source}: {count} records")
    
    # Show recent data
    print(f"\n📅 Most recent 5 days:")
    recent_data = combined_df[['close', 'volume', 'source']].tail()
    for date, row in recent_data.iterrows():
        source_info = f" ({row['source']})" if 'source' in row else ""
        print(f"   {date.date()}: ${row['close']:,.2f}{source_info}")
    
    print(f"\n✅ Successfully combined Bitcoin data!")
    print(f"📁 Saved as: {filename}")
    
    return combined_df

if __name__ == "__main__":
    combine_bitcoin_data()
