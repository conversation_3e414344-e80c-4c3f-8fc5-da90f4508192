"""Simple Bitcoin data collection script."""

import yfinance as yf
import pandas as pd
import time
from datetime import datetime
import os

def collect_bitcoin_data():
    """Collect Bitcoin price data from Yahoo Finance."""
    print("Collecting Bitcoin price data...")
    
    # Create data directory if it doesn't exist
    os.makedirs('data/raw', exist_ok=True)
    
    try:
        # Add delay to avoid rate limiting
        time.sleep(2)
        
        # Download Bitcoin data
        ticker = yf.Ticker("BTC-USD")
        data = ticker.history(start="2020-01-01", end=None, auto_adjust=False)
        
        if data.empty:
            print("No data retrieved. Trying alternative approach...")
            # Try with different parameters
            data = yf.download("BTC-USD", start="2020-01-01", progress=False, auto_adjust=False)
        
        if not data.empty:
            # Clean column names
            data.columns = [col.lower() for col in data.columns]
            
            # Save data
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'data/raw/BTC_USD_yahoo_{timestamp}.csv'
            data.to_csv(filename)
            
            print(f"✓ Successfully collected {len(data)} records")
            print(f"✓ Date range: {data.index.min()} to {data.index.max()}")
            print(f"✓ Data saved to: {filename}")
            
            # Display basic statistics
            print(f"\nPrice Statistics:")
            print(f"Current price: ${data['close'].iloc[-1]:,.2f}")
            print(f"Min price: ${data['close'].min():,.2f}")
            print(f"Max price: ${data['close'].max():,.2f}")
            print(f"Average price: ${data['close'].mean():,.2f}")
            
            return data
        else:
            print("✗ Failed to retrieve data")
            return None
            
    except Exception as e:
        print(f"✗ Error collecting data: {e}")
        return None

if __name__ == "__main__":
    collect_bitcoin_data()
