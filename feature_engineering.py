"""Comprehensive Feature Engineering for Bitcoin Price Prediction."""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_raw_data():
    """Load the combined Bitcoin data."""
    # Try combined data first
    combined_path = Path('data/raw/BTC_USD_combined_latest.csv')
    if combined_path.exists():
        print("Loading combined Bitcoin data...")
        df = pd.read_csv(combined_path, index_col=0, parse_dates=True)
        print(f"Loaded combined data shape: {df.shape}")
        return df

    # Fallback to existing processed data
    processed_path = Path('data/processed/bitcoin_preprocessed.csv')
    if processed_path.exists():
        print("Loading existing processed data...")
        df = pd.read_csv(processed_path, index_col=0, parse_dates=True)
        print(f"Loaded processed data shape: {df.shape}")
        return df

    raise FileNotFoundError("No Bitcoin data found. Run data collection first.")

def create_lag_features(df, price_col='close', lags=[1, 2, 3, 7, 14, 30]):
    """Create lagged price features."""
    print("Creating lag features...")
    
    for lag in lags:
        df[f'price_lag_{lag}'] = df[price_col].shift(lag)
        df[f'return_lag_{lag}'] = df['daily_return'].shift(lag)
        df[f'volume_lag_{lag}'] = df['volume'].shift(lag)
    
    print(f"Created {len(lags) * 3} lag features")
    return df

def create_rolling_features(df, windows=[7, 14, 21, 30, 50, 100]):
    """Create rolling statistical features."""
    print("Creating rolling features...")
    
    for window in windows:
        # Price-based rolling features
        df[f'sma_{window}'] = df['close'].rolling(window=window).mean()
        df[f'std_{window}'] = df['close'].rolling(window=window).std()
        df[f'min_{window}'] = df['close'].rolling(window=window).min()
        df[f'max_{window}'] = df['close'].rolling(window=window).max()
        
        # Return-based rolling features
        df[f'return_mean_{window}'] = df['daily_return'].rolling(window=window).mean()
        df[f'return_std_{window}'] = df['daily_return'].rolling(window=window).std()
        
        # Volume-based rolling features
        df[f'volume_mean_{window}'] = df['volume'].rolling(window=window).mean()
        df[f'volume_std_{window}'] = df['volume'].rolling(window=window).std()
        
        # Price position within range
        df[f'price_position_{window}'] = (df['close'] - df[f'min_{window}']) / (df[f'max_{window}'] - df[f'min_{window}'])
        
        # Bollinger Bands
        df[f'bb_upper_{window}'] = df[f'sma_{window}'] + (2 * df[f'std_{window}'])
        df[f'bb_lower_{window}'] = df[f'sma_{window}'] - (2 * df[f'std_{window}'])
        df[f'bb_position_{window}'] = (df['close'] - df[f'bb_lower_{window}']) / (df[f'bb_upper_{window}'] - df[f'bb_lower_{window}'])
    
    print(f"Created rolling features for {len(windows)} windows")
    return df

def create_technical_indicators(df):
    """Create technical analysis indicators."""
    print("Creating technical indicators...")
    
    # RSI (Relative Strength Index)
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    df['rsi_14'] = calculate_rsi(df['close'], 14)
    df['rsi_21'] = calculate_rsi(df['close'], 21)
    
    # MACD (Moving Average Convergence Divergence)
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_histogram'] = df['macd'] - df['macd_signal']
    
    # Stochastic Oscillator
    def calculate_stochastic(df, k_window=14, d_window=3):
        low_min = df['low'].rolling(window=k_window).min()
        high_max = df['high'].rolling(window=k_window).max()
        k_percent = 100 * ((df['close'] - low_min) / (high_max - low_min))
        d_percent = k_percent.rolling(window=d_window).mean()
        return k_percent, d_percent
    
    df['stoch_k'], df['stoch_d'] = calculate_stochastic(df)
    
    # Williams %R
    def calculate_williams_r(df, window=14):
        high_max = df['high'].rolling(window=window).max()
        low_min = df['low'].rolling(window=window).min()
        williams_r = -100 * ((high_max - df['close']) / (high_max - low_min))
        return williams_r
    
    df['williams_r'] = calculate_williams_r(df)
    
    # Commodity Channel Index (CCI)
    def calculate_cci(df, window=20):
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        sma_tp = typical_price.rolling(window=window).mean()
        mad = typical_price.rolling(window=window).apply(lambda x: np.mean(np.abs(x - x.mean())))
        cci = (typical_price - sma_tp) / (0.015 * mad)
        return cci
    
    df['cci'] = calculate_cci(df)
    
    print("Created technical indicators: RSI, MACD, Stochastic, Williams %R, CCI")
    return df

def create_volatility_features(df):
    """Create volatility-based features."""
    print("Creating volatility features...")
    
    # Historical volatility (different windows)
    for window in [7, 14, 30, 60]:
        df[f'volatility_{window}d'] = df['daily_return'].rolling(window=window).std() * np.sqrt(365)
    
    # Parkinson volatility (using high-low range)
    def parkinson_volatility(df, window=30):
        return np.sqrt((1/(4*np.log(2))) * np.log(df['high']/df['low'])**2).rolling(window=window).mean() * np.sqrt(365)
    
    df['parkinson_vol_30d'] = parkinson_volatility(df)
    
    # Garman-Klass volatility
    def garman_klass_volatility(df, window=30):
        log_hl = np.log(df['high']/df['low'])
        log_co = np.log(df['close']/df['open'])
        gk_vol = (0.5 * log_hl**2 - (2*np.log(2)-1) * log_co**2).rolling(window=window).mean()
        return np.sqrt(gk_vol * 365)
    
    df['gk_vol_30d'] = garman_klass_volatility(df)
    
    # Volatility clustering (GARCH-like features)
    df['vol_regime'] = (df['volatility_30d'] > df['volatility_30d'].rolling(window=60).mean()).astype(int)
    
    print("Created volatility features")
    return df

def create_momentum_features(df):
    """Create momentum-based features."""
    print("Creating momentum features...")
    
    # Rate of Change (ROC)
    for period in [5, 10, 20]:
        df[f'roc_{period}'] = ((df['close'] - df['close'].shift(period)) / df['close'].shift(period)) * 100
    
    # Price momentum
    for period in [5, 10, 20]:
        df[f'momentum_{period}'] = df['close'] / df['close'].shift(period)
    
    # Acceleration (second derivative of price)
    df['acceleration'] = df['daily_return'].diff()
    
    # Trend strength (using available rolling std features)
    available_windows = [7, 14, 21, 30, 50]
    for window in available_windows:
        if f'std_{window}' in df.columns:
            df[f'trend_strength_{window}'] = (df['close'] - df['close'].shift(window)) / df[f'std_{window}']
    
    print("Created momentum features")
    return df

def create_market_structure_features(df):
    """Create market microstructure features."""
    print("Creating market structure features...")
    
    # Price gaps
    df['gap'] = df['open'] - df['close'].shift(1)
    df['gap_pct'] = df['gap'] / df['close'].shift(1)
    
    # Intraday range
    df['daily_range'] = df['high'] - df['low']
    df['daily_range_pct'] = df['daily_range'] / df['close']
    
    # Body and shadow ratios (candlestick analysis)
    df['body'] = abs(df['close'] - df['open'])
    df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
    df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
    df['body_ratio'] = df['body'] / df['daily_range']
    df['upper_shadow_ratio'] = df['upper_shadow'] / df['daily_range']
    df['lower_shadow_ratio'] = df['lower_shadow'] / df['daily_range']
    
    # Volume-price relationship
    df['volume_price_trend'] = (df['volume'] * df['daily_return']).rolling(window=10).sum()
    df['price_volume_ratio'] = df['close'] / df['volume']
    
    print("Created market structure features")
    return df

def create_temporal_features(df):
    """Create time-based features."""
    print("Creating temporal features...")
    
    # Basic time features (already created in EDA, but ensuring they exist)
    if 'year' not in df.columns:
        df['year'] = df.index.year
    if 'month' not in df.columns:
        df['month'] = df.index.month
    if 'day_of_week' not in df.columns:
        df['day_of_week'] = df.index.dayofweek
    if 'day_of_year' not in df.columns:
        df['day_of_year'] = df.index.dayofyear
    
    # Cyclical encoding for temporal features
    df['month_sin'] = np.sin(2 * np.pi * df['month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['month'] / 12)
    df['day_of_week_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['day_of_week_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    df['day_of_year_sin'] = np.sin(2 * np.pi * df['day_of_year'] / 365)
    df['day_of_year_cos'] = np.cos(2 * np.pi * df['day_of_year'] / 365)
    
    # Quarter and semester
    df['quarter'] = df.index.quarter
    df['semester'] = ((df['month'] - 1) // 6) + 1
    
    # Weekend indicator
    df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
    
    # Month-end and quarter-end effects
    df['is_month_end'] = (df.index.day >= 28).astype(int)
    df['is_quarter_end'] = ((df['month'] % 3 == 0) & (df.index.day >= 28)).astype(int)
    
    print("Created temporal features")
    return df

def main():
    """Main feature engineering function."""
    print("Bitcoin Price Prediction - Feature Engineering")
    print("=" * 60)

    try:
        # Load processed data
        df = load_raw_data()

        # Ensure we have basic columns
        if 'close' not in df.columns:
            raise ValueError("Close price column not found in data")

        # Create basic features if they don't exist
        if 'daily_return' not in df.columns:
            df['daily_return'] = df['close'].pct_change()
            print("Created daily_return column")

        # Remove source column if it exists (not needed for modeling)
        if 'source' in df.columns:
            df = df.drop('source', axis=1)
        
        # Create different types of features
        df = create_lag_features(df)
        df = create_rolling_features(df)
        df = create_technical_indicators(df)
        df = create_volatility_features(df)
        df = create_momentum_features(df)
        df = create_market_structure_features(df)
        df = create_temporal_features(df)
        
        # Remove rows with NaN values (due to lags and rolling windows)
        initial_rows = len(df)
        df = df.dropna()
        final_rows = len(df)
        
        print(f"\nFeature engineering completed!")
        print(f"Initial rows: {initial_rows}")
        print(f"Final rows: {final_rows}")
        print(f"Rows removed due to NaN: {initial_rows - final_rows}")
        print(f"Total features: {len(df.columns)}")
        
        # Save engineered features
        output_path = 'data/features/bitcoin_engineered_features.csv'
        df.to_csv(output_path)
        print(f"✓ Engineered features saved to: {output_path}")
        
        # Display feature summary
        print(f"\nFeature Categories:")
        feature_categories = {
            'Original': ['open', 'high', 'low', 'close', 'volume'],
            'Lag Features': [col for col in df.columns if 'lag' in col],
            'Rolling Features': [col for col in df.columns if any(x in col for x in ['sma', 'std', 'min', 'max', 'bb_'])],
            'Technical Indicators': [col for col in df.columns if any(x in col for x in ['rsi', 'macd', 'stoch', 'williams', 'cci'])],
            'Volatility Features': [col for col in df.columns if 'vol' in col or 'volatility' in col],
            'Momentum Features': [col for col in df.columns if any(x in col for x in ['roc', 'momentum', 'acceleration', 'trend'])],
            'Market Structure': [col for col in df.columns if any(x in col for x in ['gap', 'range', 'body', 'shadow', 'volume_price'])],
            'Temporal Features': [col for col in df.columns if any(x in col for x in ['year', 'month', 'day', 'quarter', 'weekend', 'sin', 'cos'])]
        }
        
        for category, features in feature_categories.items():
            print(f"{category}: {len(features)} features")
        
        print("\n" + "="*60)
        print("FEATURE ENGINEERING COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        return df
        
    except Exception as e:
        print(f"Error during feature engineering: {e}")
        return None

if __name__ == "__main__":
    main()
