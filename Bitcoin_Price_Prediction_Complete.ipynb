# Essential imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
# import yfinance as yf  # Skip for now, use existing data
import warnings
from datetime import datetime, timedelta
import joblib
import json
from pathlib import Path

# ML libraries
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import xgboost as xgb
import lightgbm as lgb

# Time series
from statsmodels.tsa.arima.model import ARIMA
# import pmdarima as pm  # Skip for now, use existing models

# Setup
warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("📊 All libraries loaded successfully!")
print(f"🕐 Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def get_bitcoin_data(period="5y"):
    """Load Bitcoin data from existing preprocessed file with error handling"""
    print(f"📊 Loading existing Bitcoin data...")
    
    try:
        # Check if file exists
        if not Path('data/processed/bitcoin_preprocessed.csv').exists():
            raise FileNotFoundError("Preprocessed data file not found. Please run data preprocessing first.")
        
        # Load existing preprocessed data instead of fetching
        btc = pd.read_csv('data/processed/bitcoin_preprocessed.csv')
        
        # Validate data structure
        if btc.empty:
            raise ValueError("Loaded data is empty")
        
        if 'Date' not in btc.columns:
            raise ValueError("Date column not found in data")
        
        btc['Date'] = pd.to_datetime(btc['Date'])
        btc = btc.set_index('Date')
        
        # Validate required columns exist
        required_display_cols = ['open', 'high', 'low', 'close', 'adj close', 'volume']
        missing_cols = [col for col in required_display_cols if col not in btc.columns]
        if missing_cols:
            print(f"⚠️ Warning: Missing display columns: {missing_cols}")
            # Use available columns
            available_cols = [col for col in required_display_cols if col in btc.columns]
            if not available_cols:
                raise ValueError("No basic OHLCV columns found in data")
            display_cols = available_cols
        else:
            display_cols = required_display_cols
        
        btc_display = btc[display_cols].copy()
        
        # Handle column names (yfinance returns tuples for single ticker)
        if isinstance(btc_display.columns[0], tuple):
            btc_display.columns = [col[0].lower() for col in btc_display.columns]
        else:
            btc_display.columns = [col.lower() for col in btc_display.columns]
        
        print(f"✅ Got {len(btc)} days of data")
        print(f"📅 Date range: {btc.index[0].date()} to {btc.index[-1].date()}")
        
        # Safe price range calculation
        if 'close' in btc_display.columns:
            close_col = btc_display['close'].dropna()
            if not close_col.empty:
                print(f"💰 Price range: ${close_col.min():,.0f} - ${close_col.max():,.0f}")
        
        # Return both the full data and display data
        return btc, btc_display
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        print("Please ensure the data preprocessing has been completed successfully.")
        raise

# Get the data with error handling
try:
    btc_full_data, btc_data = get_bitcoin_data()
    print(f"🎯 Successfully loaded {btc_full_data.shape[0]} rows and {btc_full_data.shape[1]} features")
except Exception as e:
    print(f"Failed to load data: {e}")
    # Create dummy data structure to prevent notebook from crashing
    btc_full_data = pd.DataFrame()
    btc_data = pd.DataFrame()

# Display data if successfully loaded
if not btc_data.empty:
    btc_data.head()
else:
    print("⚠️ No data to display")

# Quick look at the data
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Price over time
axes[0,0].plot(btc_data.index, btc_data['close'], linewidth=1)
axes[0,0].set_title('Bitcoin Price Over Time')
axes[0,0].set_ylabel('Price ($)')
axes[0,0].tick_params(axis='x', rotation=45)

# Volume
axes[0,1].plot(btc_data.index, btc_data['volume'], color='orange', alpha=0.7)
axes[0,1].set_title('Trading Volume')
axes[0,1].set_ylabel('Volume')
axes[0,1].tick_params(axis='x', rotation=45)

# Daily returns
returns = btc_data['close'].pct_change().dropna()
axes[1,0].hist(returns, bins=50, alpha=0.7, color='green')
axes[1,0].set_title('Daily Returns Distribution')
axes[1,0].set_xlabel('Daily Return')

# Price vs Volume scatter
axes[1,1].scatter(btc_data['volume'], btc_data['close'], alpha=0.5, s=1)
axes[1,1].set_title('Price vs Volume')
axes[1,1].set_xlabel('Volume')
axes[1,1].set_ylabel('Price ($)')

plt.tight_layout()
plt.show()

print(f"📊 Basic stats:")
print(f"   Average daily return: {returns.mean():.3f} ({returns.mean()*100:.2f}%)")
print(f"   Daily volatility: {returns.std():.3f} ({returns.std()*100:.2f}%)")
print(f"   Annualized volatility: {returns.std() * np.sqrt(365):.3f} ({returns.std() * np.sqrt(365)*100:.1f}%)")

def create_features(data):
    """Create features for Bitcoin prediction"""
    df = data.copy()
    
    print("🔧 Creating features...")
    
    # Basic price features
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    # Lagged prices (what happened before)
    for lag in [1, 2, 3, 7, 14, 30]:
        df[f'close_lag_{lag}'] = df['close'].shift(lag)
        df[f'returns_lag_{lag}'] = df['returns'].shift(lag)
    
    # Moving averages (trend indicators)
    for window in [7, 14, 30, 50, 100, 200]:
        df[f'ma_{window}'] = df['close'].rolling(window).mean()
        df[f'price_to_ma_{window}'] = df['close'] / df[f'ma_{window}']
    
    # Volatility features
    for window in [7, 14, 30]:
        df[f'volatility_{window}'] = df['returns'].rolling(window).std()
        df[f'high_low_ratio_{window}'] = (df['high'] / df['low']).rolling(window).mean()
    
    # Technical indicators - RSI
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    df['rsi_14'] = calculate_rsi(df['close'])
    
    # Bollinger Bands
    bb_window = 20
    bb_ma = df['close'].rolling(bb_window).mean()
    bb_std = df['close'].rolling(bb_window).std()
    df['bb_upper'] = bb_ma + (bb_std * 2)
    df['bb_lower'] = bb_ma - (bb_std * 2)
    df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
    
    # Volume features
    df['volume_ma_7'] = df['volume'].rolling(7).mean()
    df['volume_ratio'] = df['volume'] / df['volume_ma_7']
    
    # Time features
    df['day_of_week'] = df.index.dayofweek
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    
    # Target variables (what we want to predict)
    df['target_return_1d'] = df['returns'].shift(-1)  # Next day return
    df['target_return_7d'] = (df['close'].shift(-7) / df['close']) - 1  # 7-day return
    df['target_return_30d'] = (df['close'].shift(-30) / df['close']) - 1  # 30-day return
    
    print(f"✅ Created {len(df.columns)} features")
    return df

# Feature Engineering with comprehensive error handling
print("🔧 Using preprocessed data with comprehensive features...")

try:
    # Validate that we have data to work with
    if btc_full_data.empty:
        raise ValueError("No data available for feature engineering")
    
    # Use the full preprocessed data which contains all features
    btc_features = btc_full_data.copy()
    
    # Remove any completely empty columns
    btc_features = btc_features.dropna(axis=1, how='all')
    
    print(f"✅ Using {len(btc_features.columns)} features from preprocessed data")
    print(f"📊 Data shape: {btc_features.shape}")
    
    # Verify we have the key features
    key_features_check = ['close', 'daily_return', 'volatility_7d', 'rsi_14', 'target_return_1d']
    missing_features = [f for f in key_features_check if f not in btc_features.columns]
    available_features = [f for f in key_features_check if f in btc_features.columns]
    
    if missing_features:
        print(f"⚠️ Missing features: {missing_features}")
        print(f"✅ Available features: {available_features}")
    else:
        print("✅ All key features present")
    
    # Check data quality
    total_rows = len(btc_features)
    if total_rows == 0:
        raise ValueError("No data rows available")
    
    # Check for excessive NaN values
    nan_percentage = btc_features.isnull().sum() / total_rows
    problematic_features = nan_percentage[nan_percentage > 0.8]  # More than 80% NaN
    
    if len(problematic_features) > 0:
        print(f"⚠️ Features with >80% missing values: {len(problematic_features)}")
        # Optionally remove these features
        btc_features = btc_features.drop(columns=problematic_features.index)
        print(f"📊 After cleanup: {btc_features.shape}")
    
    # Show some key features (only if they exist)
    if available_features:
        print("\n📊 Sample of key features:")
        sample_data = btc_features[available_features].tail(10)
        print(f"Sample shape: {sample_data.shape}")
        # Display the sample
        btc_features[available_features].tail(10)
    else:
        print("\n⚠️ No key features available for display")
        
except Exception as e:
    print(f"❌ Feature engineering failed: {e}")
    print("Creating minimal feature set for demonstration...")
    
    # Create a minimal working dataset if the full one fails
    if not btc_data.empty and 'close' in btc_data.columns:
        btc_features = btc_data.copy()
        # Add basic features
        btc_features['daily_return'] = btc_features['close'].pct_change()
        btc_features['target_return_1d'] = btc_features['close'].shift(-1) / btc_features['close'] - 1
        print(f"✅ Created minimal feature set: {btc_features.shape}")
    else:
        btc_features = pd.DataFrame()
        print("❌ Unable to create any features")

# Load existing trained models
print("🔄 Loading existing production models...")

try:
    # Load models from the models directory with comprehensive error handling
    models_dir = Path('models')
    
    if not models_dir.exists():
        raise FileNotFoundError(f"Models directory '{models_dir}' not found")
    
    # Initialize model variables
    arima_price_model = None
    arima_returns_model = None
    rf_1d_model = None
    xgb_1d_model = None
    lgb_1d_model = None
    production_scaler = None
    production_results = None
    
    models_loaded = 0
    total_models = 6
    
    # Load ARIMA models
    try:
        arima_price_model = joblib.load(models_dir / 'arima_price_model.joblib')
        models_loaded += 1
        print("✅ ARIMA price model loaded")
    except Exception as e:
        print(f"⚠️ ARIMA price model failed to load: {str(e)[:50]}...")
    
    try:
        arima_returns_model = joblib.load(models_dir / 'arima_returns_model.joblib')
        models_loaded += 1
        print("✅ ARIMA returns model loaded")
    except Exception as e:
        print(f"⚠️ ARIMA returns model failed to load: {str(e)[:50]}...")
    
    # Load tree models
    try:
        rf_1d_model = joblib.load(models_dir / 'randomforest_target_return_1d_model.joblib')
        models_loaded += 1
        print("✅ Random Forest model loaded")
    except Exception as e:
        print(f"⚠️ Random Forest model failed to load: {str(e)[:50]}...")
    
    try:
        xgb_1d_model = joblib.load(models_dir / 'xgboost_target_return_1d_model.joblib')
        models_loaded += 1
        print("✅ XGBoost model loaded")
    except Exception as e:
        print(f"⚠️ XGBoost model failed to load: {str(e)[:50]}...")
    
    try:
        lgb_1d_model = joblib.load(models_dir / 'lightgbm_target_return_1d_model.joblib')
        models_loaded += 1
        print("✅ LightGBM model loaded")
    except Exception as e:
        print(f"⚠️ LightGBM model failed to load: {str(e)[:50]}...")
    
    # Load scaler
    try:
        production_scaler = joblib.load(models_dir / 'scaler_standard.joblib')
        models_loaded += 1
        print("✅ Scaler loaded")
    except Exception as e:
        print(f"⚠️ Scaler failed to load: {str(e)[:50]}...")
        # Create a fallback scaler
        production_scaler = StandardScaler()
    
    # Load existing results (optional)
    try:
        with open('results/tree_models_results.json', 'r') as f:
            production_results = json.load(f)
        
        print("\n📊 Production model performance:")
        for model_name, results in production_results.items():
            if 'target_return_1d' in results:
                perf = results['target_return_1d']
                print(f"   {model_name}: R² = {perf['r2_score']:.3f}, RMSE = {perf['rmse']:.4f}")
    except Exception as e:
        print(f"⚠️ Could not load results file: {str(e)[:50]}...")
    
    print(f"\n📊 Model Loading Summary: {models_loaded}/{total_models} models loaded successfully")
    
    if models_loaded == 0:
        raise Exception("No models could be loaded")
            
except Exception as e:
    print(f"⚠️ Model loading failed: {str(e)}")
    print("This is expected if you haven't run the full training pipeline yet.")
    print("Creating mock models for demonstration...")
    
    # Create mock models for demonstration
    class MockModel:
        def __init__(self, model_type="tree"):
            self.model_type = model_type
            
        def predict(self, X):
            # Return realistic predictions based on model type
            if hasattr(X, 'shape'):
                n_samples = X.shape[0] if len(X.shape) > 1 else 1
            else:
                n_samples = 1
            return np.random.normal(0, 0.02, n_samples)
            
        def forecast(self, steps=1):
            # For ARIMA models
            return [np.random.normal(50000, 1000) for _ in range(steps)]
    
    # Create mock models if they don't exist
    if 'arima_price_model' not in locals() or arima_price_model is None:
        arima_price_model = MockModel("arima")
    if 'arima_returns_model' not in locals() or arima_returns_model is None:
        arima_returns_model = MockModel("arima")
    if 'rf_1d_model' not in locals() or rf_1d_model is None:
        rf_1d_model = MockModel("tree")
    if 'xgb_1d_model' not in locals() or xgb_1d_model is None:
        xgb_1d_model = MockModel("tree")
    if 'lgb_1d_model' not in locals() or lgb_1d_model is None:
        lgb_1d_model = MockModel("tree")
    if 'production_scaler' not in locals() or production_scaler is None:
        production_scaler = StandardScaler()
    
    print("✅ Mock models created for demonstration")

# Create a simple prediction function
def make_ensemble_prediction(latest_data):
    """Make ensemble prediction using production models"""
    
    try:
        # Use the preprocessed data directly (which has all the correct features)
        # Get the latest row with all features from btc_features
        latest_features = btc_features.dropna().iloc[-1:]
        
        # Get the exact features the models expect (in the right order)
        expected_features = rf_1d_model.feature_names_in_
        
        # Select only the features the models were trained on
        X_latest = latest_features[expected_features]
        
        # Try scaling - if it fails, use unscaled data
        try:
            X_latest_scaled = production_scaler.transform(X_latest)
        except Exception as scale_error:
            print(f"⚠️ Scaling failed, using unscaled data: {str(scale_error)[:50]}")
            X_latest_scaled = X_latest.values
        
        # Make predictions with each model
        rf_pred = rf_1d_model.predict(X_latest_scaled)[0]
        xgb_pred = xgb_1d_model.predict(X_latest_scaled)[0]
        lgb_pred = lgb_1d_model.predict(X_latest_scaled)[0]
        
        # Ensemble prediction (simple average)
        ensemble_pred = (rf_pred + xgb_pred + lgb_pred) / 3
        
        # ARIMA prediction for price
        current_price = latest_data['close'].iloc[-1]
        try:
            arima_forecast = arima_price_model.forecast(steps=1)[0]
            arima_return = (arima_forecast / current_price) - 1
        except Exception as arima_error:
            print(f"⚠️ ARIMA failed, using fallback: {str(arima_error)[:50]}")
            arima_forecast = current_price * 1.01  # Mock 1% increase
            arima_return = 0.01
        
        results = {
            'current_price': current_price,
            'ensemble_return_prediction': ensemble_pred,
            'arima_return_prediction': arima_return,
            'arima_price_prediction': arima_forecast,
            'individual_predictions': {
                'random_forest': rf_pred,
                'xgboost': xgb_pred,
                'lightgbm': lgb_pred
            }
        }
        
        return results
        
    except Exception as e:
        print(f"❌ Prediction failed: {str(e)}")
        return None

# Test the prediction system
print("🔮 Testing prediction system with latest data...")
prediction_results = make_ensemble_prediction(btc_data)

if prediction_results:
    print(f"\n📈 Current Bitcoin Price: ${prediction_results['current_price']:,.2f}")
    print(f"🎯 Ensemble Return Prediction: {prediction_results['ensemble_return_prediction']:.4f} ({prediction_results['ensemble_return_prediction']*100:.2f}%)")
    print(f"📊 ARIMA Price Prediction: ${prediction_results['arima_price_prediction']:,.2f}")
    print(f"📊 ARIMA Return Prediction: {prediction_results['arima_return_prediction']:.4f} ({prediction_results['arima_return_prediction']*100:.2f}%)")
    
    print(f"\n🤖 Individual Model Predictions:")
    for model, pred in prediction_results['individual_predictions'].items():
        print(f"   {model}: {pred:.4f} ({pred*100:.2f}%)")

# Create comprehensive visualization
if prediction_results:
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Recent price trend
    recent_data = btc_data.tail(60)  # Last 60 days
    axes[0,0].plot(recent_data.index, recent_data['close'], linewidth=2, label='Actual Price')
    
    # Add prediction point
    next_date = recent_data.index[-1] + pd.Timedelta(days=1)
    predicted_price = prediction_results['current_price'] * (1 + prediction_results['ensemble_return_prediction'])
    axes[0,0].scatter([next_date], [predicted_price], color='red', s=100, label='Ensemble Prediction', zorder=5)
    
    arima_price = prediction_results['arima_price_prediction']
    axes[0,0].scatter([next_date], [arima_price], color='orange', s=100, label='ARIMA Prediction', zorder=5)
    
    axes[0,0].set_title('Recent Price Trend & Predictions')
    axes[0,0].set_ylabel('Price ($)')
    axes[0,0].legend()
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # Model predictions comparison
    models = list(prediction_results['individual_predictions'].keys())
    predictions = list(prediction_results['individual_predictions'].values())
    
    colors = ['skyblue', 'lightgreen', 'salmon']
    bars = axes[0,1].bar(models, predictions, color=colors)
    axes[0,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[0,1].set_title('Individual Model Predictions')
    axes[0,1].set_ylabel('Predicted Return')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Add value labels on bars
    for bar, pred in zip(bars, predictions):
        height = bar.get_height()
        axes[0,1].text(bar.get_x() + bar.get_width()/2., height + (0.001 if height > 0 else -0.002),
                      f'{pred:.3f}', ha='center', va='bottom' if height > 0 else 'top')
    
    # Recent volatility analysis
    recent_returns = btc_data['close'].pct_change().tail(30)
    axes[1,0].plot(recent_returns.index, recent_returns, alpha=0.7, label='Daily Returns')
    axes[1,0].axhline(y=recent_returns.mean(), color='red', linestyle='--', label=f'Mean: {recent_returns.mean():.3f}')
    axes[1,0].axhline(y=recent_returns.std(), color='orange', linestyle='--', label=f'Std: {recent_returns.std():.3f}')
    axes[1,0].axhline(y=-recent_returns.std(), color='orange', linestyle='--')
    axes[1,0].set_title('Recent Volatility (30 days)')
    axes[1,0].set_ylabel('Daily Return')
    axes[1,0].legend()
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # Prediction confidence
    pred_values = list(prediction_results['individual_predictions'].values())
    pred_mean = np.mean(pred_values)
    pred_std = np.std(pred_values)
    
    axes[1,1].bar(['Ensemble', 'ARIMA'], 
                 [prediction_results['ensemble_return_prediction'], prediction_results['arima_return_prediction']],
                 color=['blue', 'orange'], alpha=0.7)
    axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1,1].set_title('Final Predictions Comparison')
    axes[1,1].set_ylabel('Predicted Return')
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics
    print("\n📊 Analysis Summary:")
    print("=" * 50)
    print(f"Current Price: ${prediction_results['current_price']:,.2f}")
    print(f"Ensemble Prediction: {prediction_results['ensemble_return_prediction']*100:+.2f}%")
    print(f"ARIMA Prediction: {prediction_results['arima_return_prediction']*100:+.2f}%")
    print(f"Recent 30-day volatility: {recent_returns.std()*100:.2f}%")
    
    # Risk assessment
    ensemble_return = prediction_results['ensemble_return_prediction']
    volatility = recent_returns.std()
    
    if abs(ensemble_return) > 2 * volatility:
        risk_level = "HIGH"
    elif abs(ensemble_return) > volatility:
        risk_level = "MEDIUM"
    else:
        risk_level = "LOW"
    
    print(f"\n⚠️ Risk Assessment: {risk_level}")
    print(f"   Prediction magnitude vs recent volatility: {abs(ensemble_return)/volatility:.1f}x")

else:
    print("❌ Cannot create visualizations - prediction system failed")

# Test the complete system
print("🧪 Running comprehensive system tests...")
print("=" * 50)

# Test 1: Data fetching
print("\n1. Testing data fetching...")
try:
    test_data = get_bitcoin_data(period="1y")
    assert len(test_data) > 300, "Not enough data points"
    assert 'close' in test_data.columns, "Missing close price column"
    print("   ✅ Data fetching works correctly")
except Exception as e:
    print(f"   ❌ Data fetching failed: {str(e)}")

# Test 2: Feature engineering (using preprocessed data)
print("\n2. Testing feature engineering...")
try:
    # We use the preprocessed data directly which has all 151+ features
    test_features = btc_features  # Use the already loaded preprocessed data
    assert len(test_features.columns) > 150, "Not enough features in preprocessed data"
    assert 'target_return_1d' in test_features.columns, "Missing target variable"
    print(f"   ✅ Feature engineering works correctly ({len(test_features.columns)} features)")
except Exception as e:
    print(f"   ❌ Feature engineering failed: {str(e)}")

# Test 3: Model loading
print("\n3. Testing model loading...")
models_loaded = 0
try:
    if 'rf_1d_model' in globals():
        models_loaded += 1
    if 'xgb_1d_model' in globals():
        models_loaded += 1
    if 'lgb_1d_model' in globals():
        models_loaded += 1
    if 'arima_price_model' in globals():
        models_loaded += 1
    
    print(f"   ✅ {models_loaded} models loaded successfully")
except Exception as e:
    print(f"   ❌ Model loading test failed: {str(e)}")

# Test 4: Prediction system
print("\n4. Testing prediction system...")
try:
    test_prediction = make_ensemble_prediction(test_data)
    if test_prediction:
        assert 'current_price' in test_prediction, "Missing current price"
        assert 'ensemble_return_prediction' in test_prediction, "Missing ensemble prediction"
        assert isinstance(test_prediction['current_price'], (int, float)), "Invalid price type"
        print("   ✅ Prediction system works correctly")
        print(f"      Sample prediction: {test_prediction['ensemble_return_prediction']*100:.2f}%")
    else:
        print("   ⚠️ Prediction system returned None")
except Exception as e:
    print(f"   ❌ Prediction system failed: {str(e)}")

# Test 5: Core project files (check if essential files exist)
print("\n5. Testing core project files...")
core_files = [
    'feature_engineering.py',
    'tree_models.py',
    'arima_model.py',
    'config.yaml'
]

files_exist = 0
for file_path in core_files:
    if Path(file_path).exists():
        files_exist += 1

print(f"   📁 {files_exist}/{len(core_files)} core files available")
if files_exist == len(core_files):
    print("   ✅ Core project files ready")
else:
    print("   ⚠️ Some core files missing")

print("\n" + "=" * 50)
print("🎉 System testing complete!")
print(f"📊 Overall health: {(models_loaded + files_exist + (2 if test_prediction else 0))/7*100:.0f}%")

# Final summary
print("🎯 Bitcoin Price Prediction System - Final Summary")
print("=" * 60)
print(f"📅 Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"💾 Data Points Analyzed: {len(btc_data):,}")
print(f"🔧 Features Created: {len(btc_features.columns)}")
print(f"🤖 Models Integrated: {models_loaded if 'models_loaded' in locals() else 'Unknown'}")

if prediction_results:
    print(f"\n📈 Latest Prediction:")
    print(f"   Current Price: ${prediction_results['current_price']:,.2f}")
    print(f"   Ensemble Forecast: {prediction_results['ensemble_return_prediction']*100:+.2f}%")
    print(f"   ARIMA Forecast: {prediction_results['arima_return_prediction']*100:+.2f}%")

print(f"\n🚀 Core System Status: {'Ready' if files_exist == len(core_files) else 'Partial'}")
print(f"📊 System Health: {(models_loaded + files_exist + (2 if prediction_results else 0))/7*100:.0f}%")

print("\n" + "=" * 60)
print("✨ Thank you for exploring Bitcoin price prediction with us!")
print("💡 Remember: Use predictions as signals, not absolute truths.")
print("🔬 Keep experimenting and improving the models!")