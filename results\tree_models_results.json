{"target_return_1d": {"RandomForest": {"best_params": {"max_depth": 10, "max_features": null, "min_samples_leaf": 4, "min_samples_split": 2, "n_estimators": 300}, "train_metrics": {"model": "RandomForest", "target": "target_return_1d_train", "mse": 0.658221241343915, "rmse": 0.8113083515802824, "mae": 0.5771151383544777, "r2": 0.39848426911700807, "mape": 177.62262472227832, "directional_accuracy": 77.30590577305905}, "test_metrics": {"model": "RandomForest", "target": "target_return_1d_test", "mse": 0.77798270824629, "rmse": 0.8820332806908648, "mae": 0.6416205222303519, "r2": -0.14520089887642063, "mape": 256.511626464289, "directional_accuracy": 52.254641909814325}, "feature_importance_top10": [{"feature": "volume_lag_30", "importance": 0.039505958898836846}, {"feature": "trend_strength_21", "importance": 0.03269140246830457}, {"feature": "bb_lower_100", "importance": 0.023751096059673994}, {"feature": "upper_shadow", "importance": 0.019218894985856196}, {"feature": "stoch_d", "importance": 0.01845779455349732}, {"feature": "return_mean_30", "importance": 0.017798781442011267}, {"feature": "return_lag_7", "importance": 0.01779251654319555}, {"feature": "return_lag_3", "importance": 0.0167233073208462}, {"feature": "lower_shadow", "importance": 0.01621010604017886}, {"feature": "return_lag_30", "importance": 0.015812215167710496}]}, "XGBoost": {"best_params": {"colsample_bytree": 0.9, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "subsample": 0.8}, "train_metrics": {"model": "XGBoost", "target": "target_return_1d_train", "mse": 0.9922647780831721, "rmse": 0.9961248807670513, "mae": 0.6938948298582258, "r2": 0.09321845645772253, "mape": 119.46052484148962, "directional_accuracy": 61.18115461181155}, "test_metrics": {"model": "XGBoost", "target": "target_return_1d_test", "mse": 0.6924284529486682, "rmse": 0.832122859287418, "mae": 0.5936946182171705, "r2": -0.019263896638419542, "mape": 139.40691508667064, "directional_accuracy": 53.05039787798409}, "feature_importance_top10": [{"feature": "parkinson_vol_30d", "importance": 0.017874885350465775}, {"feature": "price_position_7", "importance": 0.017413226887583733}, {"feature": "volume_mean_100", "importance": 0.01706947386264801}, {"feature": "std_14", "importance": 0.014359816908836365}, {"feature": "volatility_90d", "importance": 0.013985883444547653}, {"feature": "bb_lower_14", "importance": 0.013442711904644966}, {"feature": "day_of_week_sin", "importance": 0.013388939201831818}, {"feature": "volume_lag_30", "importance": 0.013354073278605938}, {"feature": "bb_upper_14", "importance": 0.013207153417170048}, {"feature": "max_30", "importance": 0.013074486516416073}]}, "LightGBM": {"best_params": {"colsample_bytree": 0.8, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "num_leaves": 31, "subsample": 0.8}, "train_metrics": {"model": "LightGBM", "target": "target_return_1d_train", "mse": 1.0152416213968722, "rmse": 1.0075919915307348, "mae": 0.6978579948810842, "r2": 0.07222105948675028, "mape": 148.31523202784683, "directional_accuracy": 63.10550763105508}, "test_metrics": {"model": "LightGBM", "target": "target_return_1d_test", "mse": 0.6891914125518994, "rmse": 0.8301755311691013, "mae": 0.5933639512380262, "r2": -0.014498930100236063, "mape": 129.0994381370767, "directional_accuracy": 54.11140583554377}, "feature_importance_top10": [{"feature": "bb_lower_100", "importance": 42}, {"feature": "trend_strength_14", "importance": 35}, {"feature": "volume_lag_30", "importance": 29}, {"feature": "trend_strength_21", "importance": 29}, {"feature": "rsi_14", "importance": 27}, {"feature": "return_mean_30", "importance": 25}, {"feature": "daily_return", "importance": 18}, {"feature": "parkinson_vol_30d", "importance": 17}, {"feature": "return_mean_7", "importance": 17}, {"feature": "return_lag_7", "importance": 17}]}}, "target_return_7d": {"RandomForest": {"best_params": {"max_depth": null, "max_features": "log2", "min_samples_leaf": 4, "min_samples_split": 2, "n_estimators": 300}, "train_metrics": {"model": "RandomForest", "target": "target_return_7d_train", "mse": 0.13378444775207562, "rmse": 0.3657655639232261, "mae": 0.25257428411282024, "r2": 0.880154010746933, "mape": 154.2843649091318, "directional_accuracy": 92.23623092236231}, "test_metrics": {"model": "RandomForest", "target": "target_return_7d_test", "mse": 0.7663088622935931, "rmse": 0.8753906912308316, "mae": 0.6733316764758853, "r2": -0.3089442297974301, "mape": 283.3678576356285, "directional_accuracy": 50.397877984084886}, "feature_importance_top10": [{"feature": "bb_lower_100", "importance": 0.022618756300912757}, {"feature": "day_of_year_cos", "importance": 0.019935191840271545}, {"feature": "return_mean_100", "importance": 0.01667611609282525}, {"feature": "day_of_year", "importance": 0.01573345147053991}, {"feature": "sma_100", "importance": 0.014604129150289559}, {"feature": "bb_upper_100", "importance": 0.014255116573573166}, {"feature": "std_100", "importance": 0.013574795844997677}, {"feature": "min_100", "importance": 0.013542164226202483}, {"feature": "volume_std_50", "importance": 0.013078732550207871}, {"feature": "bb_lower_50", "importance": 0.012830684550558665}]}, "XGBoost": {"best_params": {"colsample_bytree": 0.8, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "subsample": 0.8}, "train_metrics": {"model": "XGBoost", "target": "target_return_7d_train", "mse": 0.8278780265455009, "rmse": 0.9098780283892457, "mae": 0.6708703024308046, "r2": 0.2583752241808458, "mape": 269.8048907502613, "directional_accuracy": 69.9402786994028}, "test_metrics": {"model": "XGBoost", "target": "target_return_7d_test", "mse": 0.6773627181691151, "rmse": 0.8230204846594737, "mae": 0.62815892873965, "r2": -0.1570139209582504, "mape": 218.51119166538714, "directional_accuracy": 50.92838196286472}, "feature_importance_top10": [{"feature": "bb_lower_100", "importance": 0.020043499767780304}, {"feature": "min_30", "importance": 0.019574621692299843}, {"feature": "day_of_year", "importance": 0.018745724111795425}, {"feature": "min_100", "importance": 0.018160954117774963}, {"feature": "volume_std_30", "importance": 0.017940282821655273}, {"feature": "min_50", "importance": 0.017855091020464897}, {"feature": "day_of_year_cos", "importance": 0.017753873020410538}, {"feature": "std_21", "importance": 0.017718007788062096}, {"feature": "return_std_30", "importance": 0.016945531591773033}, {"feature": "sma_30", "importance": 0.01691696047782898}]}, "LightGBM": {"best_params": {"colsample_bytree": 0.8, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "num_leaves": 31, "subsample": 0.8}, "train_metrics": {"model": "LightGBM", "target": "target_return_7d_train", "mse": 0.8490039653649758, "rmse": 0.9214141117678716, "mae": 0.6752408544931, "r2": 0.23945031116396298, "mape": 263.2821119034396, "directional_accuracy": 69.34306569343066}, "test_metrics": {"model": "LightGBM", "target": "target_return_7d_test", "mse": 0.7127514161258911, "rmse": 0.8442460637313574, "mae": 0.6471846043989948, "r2": -0.21746191297536344, "mape": 249.91489862676946, "directional_accuracy": 55.702917771883286}, "feature_importance_top10": [{"feature": "bb_lower_100", "importance": 83}, {"feature": "day_of_year_cos", "importance": 59}, {"feature": "bb_upper_50", "importance": 42}, {"feature": "sma_100", "importance": 33}, {"feature": "day_of_year", "importance": 30}, {"feature": "volume_std_50", "importance": 26}, {"feature": "return_mean_100", "importance": 24}, {"feature": "volume_mean_7", "importance": 22}, {"feature": "max_14", "importance": 21}, {"feature": "volume_lag_1", "importance": 16}]}}, "target_return_30d": {"RandomForest": {"best_params": {"max_depth": 10, "max_features": "sqrt", "min_samples_leaf": 4, "min_samples_split": 10, "n_estimators": 300}, "train_metrics": {"model": "RandomForest", "target": "target_return_30d_train", "mse": 0.0305889641025123, "rmse": 0.1748970099873417, "mae": 0.12760883329728495, "r2": 0.9728632411797855, "mape": 133.58944970278682, "directional_accuracy": 96.41672196416722}, "test_metrics": {"model": "RandomForest", "target": "target_return_30d_test", "mse": 0.672996628939378, "rmse": 0.8203637174688907, "mae": 0.6006980236320663, "r2": -0.3870427123745557, "mape": 476.2275972315319, "directional_accuracy": 62.59946949602122}, "feature_importance_top10": [{"feature": "bb_lower_100", "importance": 0.036105296557822096}, {"feature": "min_100", "importance": 0.03295261696876087}, {"feature": "min_50", "importance": 0.02882512643508433}, {"feature": "bb_lower_50", "importance": 0.02874443717075205}, {"feature": "sma_100", "importance": 0.02586047530726941}, {"feature": "day_of_year_cos", "importance": 0.025053789589819516}, {"feature": "day_of_year", "importance": 0.024882465721294267}, {"feature": "bb_upper_100", "importance": 0.024220642255184943}, {"feature": "bb_upper_50", "importance": 0.02307031797671193}, {"feature": "sma_50", "importance": 0.022911550307957995}]}, "XGBoost": {"best_params": {"colsample_bytree": 0.8, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "subsample": 0.8}, "train_metrics": {"model": "XGBoost", "target": "target_return_30d_train", "mse": 0.5008230222966944, "rmse": 0.7076885065455666, "mae": 0.5483773210916177, "r2": 0.5556987963982714, "mape": 226.05883374987607, "directional_accuracy": 85.20238885202389}, "test_metrics": {"model": "XGBoost", "target": "target_return_30d_test", "mse": 0.5362601128455815, "rmse": 0.7322978306983993, "mae": 0.5514910497862672, "r2": -0.10522943128534146, "mape": 161.15711638687065, "directional_accuracy": 52.254641909814325}, "feature_importance_top10": [{"feature": "min_100", "importance": 0.03815075755119324}, {"feature": "month", "importance": 0.03617263585329056}, {"feature": "bb_lower_100", "importance": 0.03297378122806549}, {"feature": "volume_mean_100", "importance": 0.030193720012903214}, {"feature": "bb_upper_50", "importance": 0.02863316796720028}, {"feature": "day_of_year", "importance": 0.028121044859290123}, {"feature": "parkinson_vol_30d", "importance": 0.027031084522604942}, {"feature": "max_50", "importance": 0.02496981807053089}, {"feature": "month_cos", "importance": 0.024827448651194572}, {"feature": "volatility_30d", "importance": 0.02283153496682644}]}, "LightGBM": {"best_params": {"colsample_bytree": 0.8, "learning_rate": 0.01, "max_depth": 3, "n_estimators": 100, "num_leaves": 31, "subsample": 0.8}, "train_metrics": {"model": "LightGBM", "target": "target_return_30d_train", "mse": 0.5007462004815063, "rmse": 0.7076342278900211, "mae": 0.5482225034200243, "r2": 0.5557669482671581, "mape": 210.15438413848702, "directional_accuracy": 86.59588586595885}, "test_metrics": {"model": "LightGBM", "target": "target_return_30d_test", "mse": 0.5455453598617513, "rmse": 0.7386104249614619, "mae": 0.554611702680467, "r2": -0.12436627930592214, "mape": 183.2046840894214, "directional_accuracy": 53.315649867374006}, "feature_importance_top10": [{"feature": "min_100", "importance": 67}, {"feature": "return_std_100", "importance": 56}, {"feature": "bb_lower_100", "importance": 55}, {"feature": "volatility_90d", "importance": 35}, {"feature": "day_of_year_sin", "importance": 31}, {"feature": "day_of_year", "importance": 30}, {"feature": "parkinson_vol_30d", "importance": 30}, {"feature": "gk_vol_30d", "importance": 25}, {"feature": "month_cos", "importance": 25}, {"feature": "bb_upper_100", "importance": 24}]}}}