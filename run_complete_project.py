"""
Complete Bitcoin Price Prediction Project Runner
Executes the entire pipeline and cleans up unnecessary files
"""

import os
import sys
import subprocess
import shutil
import glob
from datetime import datetime
import pandas as pd
import json

class ProjectRunner:
    def __init__(self):
        self.project_root = os.getcwd()
        self.execution_log = []
        self.errors = []
        self.files_to_remove = []
        
    def log(self, message, level="INFO"):
        """Log execution steps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.execution_log.append(log_entry)
        
    def run_command(self, command, description):
        """Run a command and log results"""
        self.log(f"🔄 {description}")
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                self.log(f"✅ {description} - SUCCESS")
                return True, result.stdout
            else:
                self.log(f"❌ {description} - FAILED: {result.stderr}", "ERROR")
                self.errors.append(f"{description}: {result.stderr}")
                return False, result.stderr
        except subprocess.TimeoutExpired:
            self.log(f"⏰ {description} - TIMEOUT", "ERROR")
            self.errors.append(f"{description}: Timeout")
            return False, "Timeout"
        except Exception as e:
            self.log(f"❌ {description} - ERROR: {str(e)}", "ERROR")
            self.errors.append(f"{description}: {str(e)}")
            return False, str(e)
    
    def check_file_exists(self, filepath):
        """Check if file exists and log"""
        exists = os.path.exists(filepath)
        status = "✅" if exists else "❌"
        self.log(f"{status} File check: {filepath}")
        return exists
    
    def step1_data_collection(self):
        """Step 1: Collect and combine data"""
        self.log("=" * 60)
        self.log("STEP 1: DATA COLLECTION AND COMBINATION")
        self.log("=" * 60)
        
        # Use the combined data we already created
        if self.check_file_exists("data/raw/BTC_USD_combined_latest.csv"):
            self.log("✅ Combined data already exists, skipping collection")
            return True
        else:
            # Run data combination
            success, output = self.run_command("python combine_bitcoin_data.py", 
                                             "Combining Bitcoin data from multiple sources")
            return success
    
    def step2_feature_engineering(self):
        """Step 2: Feature Engineering"""
        self.log("=" * 60)
        self.log("STEP 2: FEATURE ENGINEERING")
        self.log("=" * 60)
        
        success, output = self.run_command("python feature_engineering.py", 
                                         "Running feature engineering pipeline")
        return success
    
    def step3_arima_modeling(self):
        """Step 3: ARIMA Time Series Modeling"""
        self.log("=" * 60)
        self.log("STEP 3: ARIMA TIME SERIES MODELING")
        self.log("=" * 60)
        
        success, output = self.run_command("python arima_model.py", 
                                         "Training ARIMA models")
        return success
    
    def step4_tree_modeling(self):
        """Step 4: Tree-based ML Models"""
        self.log("=" * 60)
        self.log("STEP 4: TREE-BASED ML MODELS")
        self.log("=" * 60)
        
        success, output = self.run_command("python train_all_models_fast.py",
                                         "Training tree-based models (RF, XGB, LGB)")
        return success
    
    def step5_prediction_test(self):
        """Step 5: Test Prediction System"""
        self.log("=" * 60)
        self.log("STEP 5: TESTING PREDICTION SYSTEM")
        self.log("=" * 60)
        
        success, output = self.run_command("python run_prediction.py", 
                                         "Testing prediction system")
        return success
    
    def step6_notebook_execution(self):
        """Step 6: Execute Main Notebook"""
        self.log("=" * 60)
        self.log("STEP 6: EXECUTING MAIN NOTEBOOK")
        self.log("=" * 60)
        
        success, output = self.run_command(
            "jupyter nbconvert --to notebook --execute --inplace Bitcoin_Price_Prediction_Complete.ipynb --ExecutePreprocessor.timeout=600",
            "Executing main Jupyter notebook"
        )
        return success
    
    def identify_unnecessary_files(self):
        """Identify files that are no longer necessary"""
        self.log("=" * 60)
        self.log("STEP 7: IDENTIFYING UNNECESSARY FILES")
        self.log("=" * 60)
        
        # Files that can be removed after successful execution
        potentially_unnecessary = [
            # Old data collection scripts (we have the combined version)
            "collect_bitcoin_data.py",
            "collect_bitcoin_data_alternative.py",
            
            # Old individual data files (we have combined)
            "data/raw/BTC_USD_yahoo_*.csv",
            "data/raw/BTC_USD_binance_*.csv",
            
            # Test/temporary files
            "test_final.png",
            "test_visualization.png",
            
            # Duplicate model files (keep only 1d models for main predictions)
            "models/*_target_return_7d_model.joblib",
            "models/*_target_return_30d_model.joblib",
            
            # Old result images that might be outdated
            "results/feature_importance_target_return_7d.png",
            "results/feature_importance_target_return_30d.png"
        ]
        
        for pattern in potentially_unnecessary:
            if "*" in pattern:
                files = glob.glob(pattern)
                for file in files:
                    if os.path.exists(file):
                        self.files_to_remove.append(file)
            else:
                if os.path.exists(pattern):
                    self.files_to_remove.append(pattern)
        
        self.log(f"📋 Identified {len(self.files_to_remove)} potentially unnecessary files")
        for file in self.files_to_remove:
            self.log(f"   📄 {file}")
    
    def cleanup_files(self, confirm=True):
        """Remove unnecessary files"""
        if not self.files_to_remove:
            self.log("✅ No files to remove")
            return
        
        self.log("=" * 60)
        self.log("STEP 8: CLEANING UP UNNECESSARY FILES")
        self.log("=" * 60)
        
        if confirm:
            self.log("🗑️ The following files will be removed:")
            for file in self.files_to_remove:
                self.log(f"   📄 {file}")
            
            # For automated execution, we'll be conservative and only remove clearly safe files
            safe_to_remove = [
                "test_final.png",
                "test_visualization.png"
            ]
            
            files_removed = 0
            for file in self.files_to_remove:
                if any(safe_file in file for safe_file in safe_to_remove):
                    try:
                        os.remove(file)
                        self.log(f"🗑️ Removed: {file}")
                        files_removed += 1
                    except Exception as e:
                        self.log(f"❌ Failed to remove {file}: {e}", "ERROR")
            
            self.log(f"✅ Removed {files_removed} unnecessary files")
        
    def generate_summary_report(self):
        """Generate a summary report of the execution"""
        self.log("=" * 60)
        self.log("GENERATING EXECUTION SUMMARY")
        self.log("=" * 60)
        
        # Check final project state
        critical_files = [
            "data/raw/BTC_USD_combined_latest.csv",
            "data/processed/bitcoin_preprocessed.csv",
            "models/randomforest_target_return_1d_model.joblib",
            "models/xgboost_target_return_1d_model.joblib",
            "models/lightgbm_target_return_1d_model.joblib",
            "models/arima_price_model.joblib",
            "models/scaler_standard.joblib",
            "results/tree_models_results.json",
            "results/arima_results.json"
        ]
        
        files_present = 0
        for file in critical_files:
            if os.path.exists(file):
                files_present += 1
                self.log(f"✅ {file}")
            else:
                self.log(f"❌ {file}")
        
        success_rate = (files_present / len(critical_files)) * 100
        
        # Generate summary
        summary = {
            "execution_time": datetime.now().isoformat(),
            "total_steps": 8,
            "errors": len(self.errors),
            "critical_files_present": files_present,
            "total_critical_files": len(critical_files),
            "success_rate": success_rate,
            "project_status": "SUCCESS" if success_rate >= 90 else "PARTIAL" if success_rate >= 70 else "FAILED"
        }
        
        # Save summary
        with open("execution_summary.json", "w") as f:
            json.dump(summary, f, indent=2)
        
        self.log(f"📊 Project Success Rate: {success_rate:.1f}%")
        self.log(f"🎯 Project Status: {summary['project_status']}")
        
        if self.errors:
            self.log("❌ Errors encountered:")
            for error in self.errors:
                self.log(f"   • {error}")
        
        return summary
    
    def run_complete_project(self):
        """Run the complete project pipeline"""
        self.log("🚀 STARTING COMPLETE BITCOIN PRICE PREDICTION PROJECT")
        self.log(f"📁 Project Directory: {self.project_root}")
        self.log(f"🕐 Start Time: {datetime.now()}")
        
        # Execute all steps
        steps = [
            self.step1_data_collection,
            self.step2_feature_engineering,
            self.step3_arima_modeling,
            self.step4_tree_modeling,
            self.step5_prediction_test,
            self.step6_notebook_execution
        ]
        
        for i, step in enumerate(steps, 1):
            try:
                success = step()
                if not success:
                    self.log(f"⚠️ Step {i} had issues but continuing...", "WARNING")
            except Exception as e:
                self.log(f"❌ Step {i} failed with exception: {e}", "ERROR")
                self.errors.append(f"Step {i}: {e}")
        
        # Cleanup and summary
        self.identify_unnecessary_files()
        self.cleanup_files()
        summary = self.generate_summary_report()
        
        self.log("🎉 PROJECT EXECUTION COMPLETED!")
        return summary

if __name__ == "__main__":
    runner = ProjectRunner()
    summary = runner.run_complete_project()
